<script lang="ts">
  import { enhance } from '$app/forms';
  import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, PageHeader, FormInput, AlertMessage, Modal, BackButton, PDFViewerModal } from '$lib/components/ui';
  import type { ProjectGroup, AssignedStudent } from '$lib/types';

  let { data } = $props();
  let project = $state(data.project);
  let selectedSubmissions = $state<string[]>([]);
  let isDownloading = $state(false);

  let showPdfViewer = $state(false);
  let currentPdfUrl = $state('');
  let currentPdfTitle = $state('');
  let currentSubmissionId = $state('');

  let isAnalyzing = $state(false);
  let complexityAnalysisData = $state<any>(null);

  let isEditingProject = $state(false);
  let projectName = $state(data.project.name);
  let projectDescription = $state(data.project.description || '');
  let maxAttempts = $state(data.project.maxAttempts || 0);

  const formatDatetimeLocal = (date: string | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    return new Date(d.getTime() - d.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
  };

  let deadline = $state(formatDatetimeLocal(data.project.deadline));
  let selectedGroups = $state<string[]>([]);
  let selectedStudents = $state<string[]>([]);
  let showDeleteConfirm = $state(false);

  let isToggling = $state(false);
  let showGroupMembers = $state<Record<string, boolean>>({});

  // Initialize selected students and groups from data
  $effect(() => {
    selectedGroups = data.projectGroups.map((pg: ProjectGroup) => pg.groupId);
    selectedStudents = data.assignedStudents.map((as: AssignedStudent) => as.studentId);
  });

  type AssignedGroupData = {
    projectGroup: ProjectGroup;
    groupStudents: Array<{ id: string; name?: string; username?: string; email: string }>;
    assignedStudentsInGroup: Array<{ id: string; name?: string; username?: string; email: string }>;
    isWholeGroupAssigned: boolean;
    isPartiallyAssigned: boolean;
  };

  type IndividualStudentGroup = {
    groupId: string;
    groupName: string;
    students: Array<{ id: string; name?: string; username?: string; email: string }>;
    groupDescription?: string | null;
  };

  // Use derived state for better reactivity
  const assignedGroupsData = $derived(() => {
    return data.projectGroups.map(projectGroup => {
      const groupStudents = data.groupStudents.find(gs => gs.groupId === projectGroup.groupId)?.students || [];
      const assignedStudentsInGroup = groupStudents.filter((student: { id: string }) => selectedStudents.includes(student.id));
      const isWholeGroupAssigned = assignedStudentsInGroup.length === groupStudents.length && groupStudents.length > 0;
      const isPartiallyAssigned = assignedStudentsInGroup.length > 0 && assignedStudentsInGroup.length < groupStudents.length;

      return {
        projectGroup,
        groupStudents,
        assignedStudentsInGroup,
        isWholeGroupAssigned,
        isPartiallyAssigned
      };
    }).filter(group => group.assignedStudentsInGroup.length > 0);
  });

  const individualStudentsByGroup = $derived(() => {
    // Collect all students assigned through groups
    const assignedGroupStudentIds = new Set<string>();
    data.projectGroups.forEach(projectGroup => {
      const groupStudents = data.groupStudents.find(gs => gs.groupId === projectGroup.groupId)?.students || [];
      const assignedStudentsInGroup = groupStudents.filter((student: { id: string }) => selectedStudents.includes(student.id));
      assignedStudentsInGroup.forEach((student: { id: string }) => assignedGroupStudentIds.add(student.id));
    });

    // Find individually assigned students (not part of any assigned group)
    const individualStudents = data.assignedStudents.filter((as: AssignedStudent) => !assignedGroupStudentIds.has(as.studentId));

    // Group individual students by groups
    const groupMap = new Map<string, IndividualStudentGroup>();

    for (const student of individualStudents) {
      for (const groupData of data.groupStudents) {
        const studentInGroup = groupData.students.find((s: { id: string }) => s.id === student.studentId);
        if (!studentInGroup) continue;

        if (!groupMap.has(groupData.groupId)) {
          groupMap.set(groupData.groupId, {
            groupId: groupData.groupId,
            groupName: groupData.groupName,
            students: [],
            groupDescription: data.projectGroups.find(pg => pg.groupId === groupData.groupId)?.group?.description ||
                             data.availableGroups.find(g => g.id === groupData.groupId)?.description
          });
        }

        const group = groupMap.get(groupData.groupId)!;
        if (!group.students.some((s: { id: string }) => s.id === studentInGroup.id)) {
          group.students.push(studentInGroup);
        }
      }
    }

    return Array.from(groupMap.values());
  });

  const toggleGroupMembers = (groupId: string) => {
    showGroupMembers[groupId] = !showGroupMembers[groupId];
    showGroupMembers = {...showGroupMembers};
  };

  const toggleStudentSelection = (studentId: string) => {
    selectedStudents = selectedStudents.includes(studentId)
      ? selectedStudents.filter(id => id !== studentId)
      : [...selectedStudents, studentId];
    updateSelectedGroups();
  };

  // Create reactive state for group selection states
  const groupSelectionStates = $derived(() => {
    const states = new Map<string, { allSelected: boolean; anySelected: boolean; isIndeterminate: boolean }>();

    // Process all groups (both project groups and available groups)
    const allGroups = [
      ...data.projectGroups.map(pg => pg.groupId),
      ...data.availableGroups.map(g => g.id)
    ];

    allGroups.forEach(groupId => {
      const groupStudents = data.groupStudents.find(gs => gs.groupId === groupId)?.students || [];
      const allSelected = groupStudents.length > 0 && groupStudents.every((s: { id: string }) => selectedStudents.includes(s.id));
      const anySelected = groupStudents.length > 0 && groupStudents.some((s: { id: string }) => selectedStudents.includes(s.id));
      const isIndeterminate = anySelected && !allSelected;

      states.set(groupId, { allSelected, anySelected, isIndeterminate });
    });

    return states;
  });

  // Helper functions that use the reactive state
  function areAllStudentsSelected(groupId: string): boolean {
    return groupSelectionStates().get(groupId)?.allSelected ?? false;
  }

  function isGroupIndeterminate(groupId: string): boolean {
    return groupSelectionStates().get(groupId)?.isIndeterminate ?? false;
  }

  function handleGroupSelection(e: Event, groupId: string) {
    const checkbox = e.target as HTMLInputElement;
    const groupStudents = data.groupStudents.find(gs => gs.groupId === groupId)?.students || [];
    const studentIds = groupStudents.map((s: { id: string }) => s.id);

    if (checkbox.checked) {
      // Add students that aren't already selected
      const newStudentIds = studentIds.filter((id: string) => !selectedStudents.includes(id));
      if (newStudentIds.length > 0) selectedStudents = [...selectedStudents, ...newStudentIds];
    } else {
      // Remove all students from this group
      selectedStudents = selectedStudents.filter((id: string) => !studentIds.includes(id));
      selectedGroups = selectedGroups.filter(id => id !== groupId);
    }

    updateSelectedGroups();
  }

  const updateSelectedGroups = () =>
    selectedGroups = [...data.projectGroups.map(pg => pg.groupId), ...data.availableGroups.map(g => g.id)]
      .filter(groupId => areAllStudentsSelected(groupId));

  // Action to set indeterminate state on checkboxes
  function setIndeterminate(node: HTMLInputElement, isIndeterminate: boolean) {
    node.indeterminate = isIndeterminate;
    return {
      update(newIsIndeterminate: boolean) {
        node.indeterminate = newIsIndeterminate;
      }
    };
  }

  const formatDate = (dateString: string | Date | null | undefined): string =>
    !dateString ? 'N/A' : new Date(dateString).toLocaleDateString();

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  interface Submission {
    id: string;
    filePath: string;
    originalFilename: string;
    studentName: string;
    hasComplexityAnalysis: boolean;
  }

  async function viewPdf(submission: Submission) {
    const pathParts = submission.filePath.split('/');
    const studentId = pathParts[2];
    currentPdfUrl = `/api/pdf/${studentId}/${pathParts.slice(3).join('/')}`;
    currentPdfTitle = `${submission.originalFilename} - ${submission.studentName}`;
    currentSubmissionId = submission.id;
    showPdfViewer = true;
    complexityAnalysisData = null;
    if (submission.hasComplexityAnalysis) await loadComplexityAnalysis(submission.id);
  }

  async function loadComplexityAnalysis(submissionId: string) {
    isAnalyzing = true;
    try {
      const response = await fetch('/api/analyze-submission', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ submissionId })
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to analyze submission');
      }
      complexityAnalysisData = await response.json();
    } catch (err) {
      console.error('Error analyzing submission:', err);
      complexityAnalysisData = { error: (err as Error).message || 'An unknown error occurred' };
    } finally {
      isAnalyzing = false;
    }
  }

  function closePdfViewer() {
    showPdfViewer = false;
    complexityAnalysisData = null;
  }

  async function analyzeSubmission(detail: { submissionId: string }) {
    if (!detail.submissionId) return;
    await loadComplexityAnalysis(detail.submissionId);
    const submissionIndex = data.submissions.findIndex((s: Submission) => s.id === detail.submissionId);
    if (submissionIndex !== -1) data.submissions[submissionIndex].hasComplexityAnalysis = true;
  }

  function toggleSubmissionSelection(submissionId: string) {
    selectedSubmissions = selectedSubmissions.includes(submissionId)
      ? selectedSubmissions.filter(id => id !== submissionId)
      : [...selectedSubmissions, submissionId];
  }

  const selectAllSubmissions = () => selectedSubmissions = data.submissions.map((s: Submission) => s.id);
  const deselectAllSubmissions = () => selectedSubmissions = [];

  async function downloadSelectedSubmissions() {
    if (selectedSubmissions.length === 0) return;
    isDownloading = true;

    try {
      const response = await fetch('/api/download-selected-cs-files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ submissionIds: selectedSubmissions })
      });

      if (!response.ok) throw new Error(await response.text() || 'Failed to download files');

      const blob = await response.blob();
      let filename = 'student_code.zip';
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch?.[1]) filename = filenameMatch[1];
      }

      // Create download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading files:', err);
      alert(`Failed to download files: ${(err as Error).message || 'Unknown error'}`);
    } finally {
      isDownloading = false;
    }
  }

  function handleSubmit() {
    return async ({ result, update }: { result: any; update: () => void }) => {
      update();

      if (result.type === 'success' && result.data?.success) {
        if (result.data.redirect) {
          window.location.href = result.data.redirect;
          return;
        }

        if (isEditingProject) {
          isEditingProject = false;
          try {
            // Fetch all updated data
            const [projectRes, studentsRes, groupsRes] = await Promise.all([
              fetch(`/api/projects/${project.id}`),
              fetch(`/api/projects/${project.id}/assigned-students`),
              fetch(`/api/projects/${project.id}/groups`)
            ]);

            if (projectRes.ok) project = await projectRes.json();

            if (studentsRes.ok) {
              data.assignedStudents = await studentsRes.json();
              selectedStudents = data.assignedStudents.map((as: AssignedStudent) => as.studentId);
            }

            if (groupsRes.ok) {
              data.projectGroups = await groupsRes.json();
              updateSelectedGroups();
            }
          } catch (error) {
            console.error('Error fetching updated project data:', error);
          }
        }

        if (showDeleteConfirm) showDeleteConfirm = false;
      }
    };
  }
</script>

<div class="space-y-8">
  <Card>
    <div class="flex justify-between items-start">
      <div>
        <PageHeader
          title={project.name}
          subtitle={project.description || undefined}
          class="mb-0"
        />
        <div class="mt-4 flex flex-wrap items-center text-sm text-gray-500">
          <span>Created: {formatDate(project.createdAt)}</span>
          <span class="mx-2">•</span>
          <span>Last updated: {formatDate(project.updatedAt)}</span>

          {#if project.maxAttempts && project.maxAttempts > 0}
            <span class="mx-2">•</span>
            <span>Max attempts: {project.maxAttempts}</span>
          {/if}

          {#if project.deadline}
            <span class="mx-2">•</span>
            <span>
              Deadline: {new Date(project.deadline).toLocaleString()}
              {#if new Date() < new Date(project.deadline)}
                (<Countdown deadline={project.deadline} showSeconds={false} warningThreshold={60} />)
              {:else}
                <span class="text-red-600">(Expired)</span>
              {/if}
            </span>
          {/if}

          <span class="mx-2">•</span>
          <span>
            Status:
            {#if project.isHidden}
              <span class="text-red-600 font-medium">Hidden from students</span>
            {:else}
              <span class="text-green-600 font-medium">Visible to students</span>
            {/if}
          </span>
        </div>
      </div>

      <div class="flex space-x-2">
        <Button
          onClick={() => {
            projectName = project.name;
            projectDescription = project.description || '';
            maxAttempts = project.maxAttempts || 0;
            deadline = formatDatetimeLocal(project.deadline);

            // Initialize selected students
            selectedStudents = data.assignedStudents.map((as: AssignedStudent) => as.studentId);
            updateSelectedGroups();

            const newShowGroupMembers = {...showGroupMembers};
            data.projectGroups.forEach((pg: ProjectGroup) => {
              newShowGroupMembers[pg.groupId] = true;
            });
            showGroupMembers = newShowGroupMembers;
            isEditingProject = true;
          }}
        >
          Edit Project
        </Button>

        <form method="POST" action="?/toggleVisibility" use:enhance={() => {
          isToggling = true;
          return async ({ result }) => {
            isToggling = false;
            if (result.type === 'success' && result.data && typeof result.data.isHidden === 'boolean') {
              project = {
                ...project,
                isHidden: result.data.isHidden
              };
            }
          };
        }}>
          <Button
            type="submit"
            disabled={isToggling}
          >
            {project.isHidden ? 'Make Visible to Students' : 'Hide from Students'}
          </Button>
        </form>

        <Button
          variant="danger"
          onClick={() => showDeleteConfirm = true}
        >
          Delete Project
        </Button>
      </div>
    </div>
  </Card>

  <Modal
    show={showDeleteConfirm}
    title="Delete Project"
    size="md"
    onClose={() => showDeleteConfirm = false}
  >
    <div class="p-4">
      <AlertMessage
        type="warning"
        message="Are you sure you want to delete this project? This action cannot be undone. All group and student assignments will be permanently removed."
      />

      <div class="flex justify-end space-x-3 mt-6">
        <Button
          onClick={() => showDeleteConfirm = false}
          variant="light"
        >
          Cancel
        </Button>

        <form method="POST" action="?/deleteProject" use:enhance={handleSubmit}>
          <Button
            type="submit"
            variant="danger"
          >
            Delete Project
          </Button>
        </form>
      </div>
    </div>
  </Modal>

  {#if isEditingProject}
    <Card>
      <h2 class="text-lg font-medium text-gray-900 mb-4">Edit Project</h2>

      <form method="POST" action="?/updateProject" use:enhance={handleSubmit} class="space-y-6">
        <FormInput
          id="name"
          name="name"
          label="Project Name"
          value={projectName}
          onChange={(e: Event) => projectName = (e.target as HTMLInputElement).value}
          required
        />

        <div>
          <label for="description" class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2">
            Project Description <span class="text-gray-500">(optional)</span>
          </label>
          <textarea
            id="description"
            name="description"
            bind:value={projectDescription}
            rows="3"
            class="appearance-none relative block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 text-base"
          ></textarea>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormInput
            id="maxAttempts"
            name="maxAttempts"
            type="number"
            label="Max Submission Attempts"
            helpText="0 = unlimited"
            value={maxAttempts.toString()}
            onChange={(e: Event) => maxAttempts = parseInt((e.target as HTMLInputElement).value) || 0}
            min={0}
          />

          <FormInput
            id="deadline"
            name="deadline"
            type="datetime-local"
            label="Submission Deadline"
            helpText="Optional"
            value={deadline}
            onChange={(e: Event) => deadline = (e.target as HTMLInputElement).value}
          />
        </div>

          <div>
            <h3 class="text-sm font-medium text-gray-700 mb-4">Assign Groups and Students</h3>

            {#if data.projectGroups.length === 0 && data.availableGroups.length === 0}
              <p class="text-sm text-gray-500 italic">No groups available. <a href="/dashboard/lecturer/groups/new" class="text-blue-600 hover:text-blue-800">Create a group</a> first.</p>
            {:else}
              <div class="space-y-4 max-h-80 overflow-y-auto border border-gray-200 rounded-md p-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Available Groups</h4>

                {#if true}
                  {@const allGroups = [
                    ...data.projectGroups.map((pg: ProjectGroup) => ({
                      id: pg.groupId,
                      name: pg.group.name,
                      description: pg.group.description,
                      isAssigned: true
                    })),
                    ...data.availableGroups.map((g: {id: string, name: string, description?: string}) => ({
                      id: g.id,
                      name: g.name,
                      description: g.description,
                      isAssigned: false
                    }))
                  ]}

                  {@const sortedGroups = allGroups.sort((a, b) => a.name.localeCompare(b.name))}

                  {#each sortedGroups as group}
                    {@const groupId = group.id}
                    {@const groupName = group.name}
                    {@const groupDescription = group.description}
                    {@const isAssigned = group.isAssigned}

                    <div class="border border-gray-200 rounded-md p-3">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center">
                          <input
                            type="checkbox"
                            id={`group-${groupId}`}
                            name="groups[]"
                            value={groupId}
                            checked={areAllStudentsSelected(groupId)}
                            onclick={(e: Event) => handleGroupSelection(e, groupId)}
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            use:setIndeterminate={isGroupIndeterminate(groupId)}
                          />
                          <label for={`group-${groupId}`} class="ml-3 block text-sm font-medium text-gray-700">
                            {groupName}
                            {#if isAssigned}
                              <span class="ml-2 px-1.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">Assigned</span>
                            {/if}
                            <!-- Debug info -->
                            {#if true}
                              {@const groupStudents = data.groupStudents.find(gs => gs.groupId === groupId)?.students || []}
                              {@const selectedCount = groupStudents.filter((s: { id: string }) => selectedStudents.includes(s.id)).length}
                              {#if groupStudents.length > 0}
                                <span class="ml-2 text-xs text-gray-500">
                                  ({selectedCount}/{groupStudents.length})
                                  {#if areAllStudentsSelected(groupId)}
                                    ✓ All
                                  {:else if selectedCount > 0}
                                    ⚬ Partial
                                  {:else}
                                    ○ None
                                  {/if}
                                </span>
                              {/if}
                            {/if}
                          </label>
                        </div>
                        <Button
                          onClick={() => toggleGroupMembers(groupId)}
                          size="md"
                        >
                          {showGroupMembers[groupId] ? 'Hide Students' : 'Show Students'}
                        </Button>
                      </div>

                      {#if groupDescription}
                        <p class="text-sm text-gray-500 ml-7 mt-1">{groupDescription}</p>
                      {/if}

                      {#if showGroupMembers[groupId]}
                        {#if true}
                          {@const groupStudentsList = data.groupStudents.find(gs => gs.groupId === groupId)?.students || []}
                          {@const selectedCount = selectedStudents.filter(id =>
                            groupStudentsList.some((s: { id: string }) => s.id === id)
                          ).length}
                          <div class="mt-3 ml-7 pl-3 border-l-2 border-gray-200">

                          <p class="text-sm font-medium text-gray-700 mb-2">
                            Students in this group:
                            <span class="text-xs text-gray-500">
                              ({selectedCount} of {groupStudentsList.length} selected)
                            </span>
                            {#if selectedCount === groupStudentsList.length && groupStudentsList.length > 0}
                              <span class="ml-1 px-1.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">All selected</span>
                            {:else if selectedCount > 0}
                              <span class="ml-1 px-1.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Partial</span>
                            {/if}
                          </p>

                          {#if groupStudentsList.length === 0}
                            <p class="text-sm text-gray-500 italic">No students in this group.</p>
                          {:else}
                            <div class="space-y-2 max-h-60 overflow-y-auto">
                              <div class="mb-2">
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    groupStudentsList.forEach((student: { id: string }) => {
                                      if (!selectedStudents.includes(student.id)) {
                                        selectedStudents = [...selectedStudents, student.id];
                                      }
                                    });
                                    updateSelectedGroups();
                                  }}
                                >
                                  Select All
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    selectedStudents = selectedStudents.filter(id =>
                                      !groupStudentsList.some((s: { id: string }) => s.id === id)
                                    );
                                    updateSelectedGroups();
                                  }}
                                >
                                  Deselect All
                                </Button>
                              </div>

                              {#each groupStudentsList as student}
                                <div class="flex items-center">
                                  <input
                                    type="checkbox"
                                    id={`student-${student.id}-${groupId}`}
                                    value={student.id}
                                    checked={selectedStudents.includes(student.id)}
                                    onclick={() => toggleStudentSelection(student.id)}
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  />
                                  <label for={`student-${student.id}-${groupId}`} class="ml-3 block text-sm text-gray-700">
                                    {student.name || student.username}
                                    <span class="text-xs text-gray-500 ml-1">({student.email})</span>
                                  </label>
                                </div>
                              {/each}
                            </div>
                          {/if}
                        </div>
                        {/if}
                      {/if}
                    </div>
                  {/each}
                {/if}
              </div>
            {/if}
          </div>

          <!-- Hidden inputs for all selected students to ensure they're submitted even when lists are collapsed -->
          {#each selectedStudents as studentId}
            <input type="hidden" name="students[]" value={studentId} />
          {/each}

          <div class="flex justify-end space-x-3">
            <Button
              onClick={() => isEditingProject = false}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
            >
              Save Changes
            </Button>
          </div>
        </form>
    </Card>
  {/if}

  <Card>
    <h2 class="text-lg font-medium text-gray-900 mb-4">Assigned Groups and Students</h2>

    {#if assignedGroupsData().length === 0 && individualStudentsByGroup().length === 0}
      <div class="text-center text-gray-500">
        <p>No students have been assigned to this project yet.</p>
        <p class="mt-2">Click "Edit Project" to assign groups and students.</p>
      </div>
    {:else}
      <div class="space-y-6">
        <!-- Display assigned groups -->
        {#each assignedGroupsData() as group}
          <div class="border border-gray-200 rounded-md p-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div class="flex items-center">
                  <h3 class="text-lg font-medium text-gray-900">{group.projectGroup.group.name}</h3>
                  {#if group.isWholeGroupAssigned}
                    <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">All students assigned</span>
                  {:else if group.isPartiallyAssigned}
                    <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                      {group.assignedStudentsInGroup.length} of {group.groupStudents.length} students assigned
                    </span>
                  {/if}
                </div>
                {#if group.projectGroup.group.description}
                  <p class="mt-1 text-sm text-gray-500">{group.projectGroup.group.description}</p>
                {/if}
              </div>
              <div class="flex justify-end items-start">
                <a
                  href={`/dashboard/lecturer/groups/${group.projectGroup.groupId}`}
                  class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 shadow-sm transition-colors duration-200"
                >
                  Manage Group
                </a>
              </div>
            </div>

            <div class="mt-4">
              <Button
                size="md"
                onClick={() => toggleGroupMembers(group.projectGroup.groupId)}
              >
                {showGroupMembers[group.projectGroup.groupId] ? 'Hide Students' : 'Show Students'}
                ({group.assignedStudentsInGroup.length} assigned)
              </Button>

              {#if showGroupMembers[group.projectGroup.groupId]}
                <div class="mt-2 pl-4 border-l-2 border-gray-200">
                  <ul class="space-y-2">
                    {#each group.assignedStudentsInGroup as student}
                      <li class="text-sm flex items-center">
                        <span class="inline-flex items-center">
                          <span class="w-2 h-2 rounded-full mr-2 bg-green-500"></span>
                          <span class="font-medium">{student.name || student.username}</span>
                          <span class="text-gray-500 ml-2">{student.email}</span>
                        </span>
                      </li>
                    {/each}
                  </ul>
                </div>
              {/if}
            </div>
          </div>
        {/each}

        <!-- Display individual students grouped by their groups -->
        {#each individualStudentsByGroup() as group}
          <div class="border border-gray-200 rounded-md p-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div class="flex items-center">
                  <h3 class="text-lg font-medium text-gray-900">{group.groupName}</h3>
                  <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                    {group.students.length} individual students assigned
                  </span>
                </div>

                {#if group.groupDescription}
                  <p class="mt-1 text-sm text-gray-500">{group.groupDescription}</p>
                {/if}
              </div>
              <div class="flex justify-end items-start">
                <a
                  href={`/dashboard/lecturer/groups/${group.groupId}`}
                  class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 shadow-sm transition-colors duration-200"
                >
                  Manage Group
                </a>
              </div>
            </div>

            <div class="mt-4">
              <Button
                size="md"
                onClick={() => toggleGroupMembers(group.groupId)}
              >
                {showGroupMembers[group.groupId] ? 'Hide Students' : 'Show Students'}
                ({group.students.length} assigned)
              </Button>

              {#if showGroupMembers[group.groupId]}
                <div class="mt-2 pl-4 border-l-2 border-gray-200">
                  <ul class="space-y-2">
                    {#each group.students as student}
                      <li class="text-sm flex items-center">
                        <span class="inline-flex items-center">
                          <span class="w-2 h-2 rounded-full mr-2 bg-green-500"></span>
                          <span class="font-medium">{student.name || student.username}</span>
                          <span class="text-gray-500 ml-2">{student.email}</span>
                        </span>
                      </li>
                    {/each}
                  </ul>
                </div>
              {/if}
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </Card>



  <Card>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-medium text-gray-900">Submissions</h2>

      {#if data.submissions.length > 0}
        <div class="flex items-center space-x-2">
          <div class="flex space-x-2">
            <Button
              size="sm"
              onClick={selectAllSubmissions}
            >
              Select All
            </Button>
            <Button
              size="sm"
              onClick={deselectAllSubmissions}
            >
              Deselect All
            </Button>
          </div>

          <Button
            variant="info"
            size="sm"
            onClick={downloadSelectedSubmissions}
            disabled={isDownloading}
          >
              Download All Code ({selectedSubmissions.length} selected)
          </Button>
        </div>
      {/if}
    </div>

    <div class="overflow-x-auto">
      {#if data.submissions.length === 0}
        <div class="p-6 text-center text-gray-500">
          No submissions have been received for this project yet.
        </div>
      {:else}
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={selectedSubmissions.length === data.submissions.length && data.submissions.length > 0}
                    use:setIndeterminate={selectedSubmissions.length > 0 && selectedSubmissions.length < data.submissions.length}
                    onclick={(e: Event) => {
                      const target = e.target as HTMLInputElement;
                      if (target.checked) {
                        selectAllSubmissions();
                      } else {
                        deselectAllSubmissions();
                      }
                    }}
                  />
                </div>
              </th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Analysis</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {#each data.submissions as submission}
              <tr class={selectedSubmissions.includes(submission.id) ? 'bg-blue-50' : ''}>
                <td class="px-4 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      checked={selectedSubmissions.includes(submission.id)}
                      onclick={() => toggleSubmissionSelection(submission.id)}
                    />
                  </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{submission.studentName}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div class="flex items-center">
                    <span class="mr-2">{submission.originalFilename}</span>
                    <button
                      class="btn-link text-blue-600 hover:text-blue-800 focus:outline-none"
                      onclick={() => viewPdf(submission)}
                      title="View PDF"
                      aria-label="View PDF document"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                  </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{formatFileSize(submission.fileSize)}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(submission.submittedAt)}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm">
                  {#if submission.hasComplexityAnalysis}
                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                      Analyzed
                    </span>
                  {:else}
                    <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                      Not analyzed
                    </span>
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      {/if}
    </div>
  </Card>

  <div class="flex justify-end mt-6">
    <BackButton href="/dashboard/lecturer/projects" label="Back to Projects" />
  </div>

  <PDFViewerModal
    show={showPdfViewer}
    url={currentPdfUrl}
    title={currentPdfTitle}
    submissionId={currentSubmissionId}
    isAnalyzing={isAnalyzing}
    complexityData={complexityAnalysisData}
    userRole="lecturer"
    onclose={closePdfViewer}
    onanalyze={analyzeSubmission}
  />
</div>