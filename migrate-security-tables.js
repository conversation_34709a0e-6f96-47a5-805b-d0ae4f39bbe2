#!/usr/bin/env node

/**
 * Security Tables Migration Script
 * 
 * This script creates the security tables and indexes for the authentication system.
 * It uses the existing database connection and runs the SQL migration directly.
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

const databaseUrl = process.env.DATABASE_URL || 'postgres://root:mysecretpassword@localhost:5432/local';

console.log('🔐 Security Tables Migration Script');
console.log('=====================================');
console.log(`📊 Database: ${databaseUrl.replace(/\/\/.*?@/, '//****:****@')}`);

async function runMigration() {
  let client;
  
  try {
    // Create database connection
    console.log('🔌 Connecting to database...');
    client = postgres(databaseUrl, {
      max: 1,
      idle_timeout: 20,
      prepare: false
    });

    // Test connection
    await client`SELECT 1`;
    console.log('✅ Database connection successful');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'src', 'lib', 'server', 'db', 'migrations', 'add_security_tables.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration file loaded successfully');

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔧 Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.trim()) {
        try {
          console.log(`   ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
          await client.unsafe(statement);
        } catch (error) {
          // Some statements might fail if they already exist, which is okay
          if (error.message.includes('already exists')) {
            console.log(`   ⚠️  Skipped (already exists): ${statement.substring(0, 30)}...`);
          } else {
            console.error(`   ❌ Failed: ${error.message}`);
            throw error;
          }
        }
      }
    }

    console.log('✅ Migration completed successfully!');

    // Verify the tables were created
    console.log('🔍 Verifying table creation...');
    
    const tables = await client`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('login_attempt', 'account_lockout')
      ORDER BY table_name
    `;

    if (tables.length === 2) {
      console.log('✅ Security tables verified:');
      tables.forEach(table => {
        console.log(`   📋 ${table.table_name}`);
      });
    } else {
      console.log('⚠️  Warning: Expected 2 tables, found:', tables.length);
    }

    // Verify indexes were created
    console.log('🔍 Verifying index creation...');
    
    const indexes = await client`
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename IN ('login_attempt', 'account_lockout')
      AND indexname LIKE 'idx_%'
      ORDER BY indexname
    `;

    console.log(`✅ Security indexes created: ${indexes.length}`);
    indexes.forEach(index => {
      console.log(`   🔗 ${index.indexname}`);
    });

    // Test the security functions
    console.log('🧪 Testing security functions...');
    
    try {
      const stats = await client`SELECT * FROM get_security_stats()`;
      console.log('✅ Security statistics function working');
      console.log('   📊 Stats:', stats[0]);
    } catch (error) {
      console.log('⚠️  Security functions may need manual verification');
    }

    console.log('\n🎉 Security migration completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Restart your application to use the new security features');
    console.log('   2. Verify login attempt tracking is working');
    console.log('   3. Test account lockout functionality');
    console.log('   4. Monitor database performance with the new indexes');

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Ensure the database is running');
    console.error('   2. Check DATABASE_URL in .env file');
    console.error('   3. Verify database credentials');
    console.error('   4. Check if tables already exist');
    
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { runMigration };
