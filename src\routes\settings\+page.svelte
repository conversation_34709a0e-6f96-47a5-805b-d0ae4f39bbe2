<script lang="ts">
	import { enhance } from '$app/forms';
	import { locale } from '$lib/stores/locale';
	import type { PageData } from './$types';
	import { slide } from 'svelte/transition';
	import {
		Button,
		FormInput,
		AlertMessage,
		Card,
		PageHeader,
		LanguageSelector,
		ThemeSwitcher
	} from '$lib/components/ui';

	let { data, form } = $props<{ data: PageData, form: any }>();
	let orgSearchTerm = $state('');
	let selectedOrganizationId = $state(data.user.organization || '');
	let showSuggestions = $state(false);
	let filteredOrganizations = $state<Array<{ id: string, name: string }>>([]);

	$effect(() => {
		if (data?.organizations) {
			filteredOrganizations = data.organizations.filter((org: { name: string }) =>
				org.name.toLowerCase().includes(orgSearchTerm.toLowerCase())
			);
		} else {
			filteredOrganizations = [];
		}
	});

	$effect(() => {
		if (data?.user?.organization && data?.organizations) {
			const currentOrg = data.organizations.find((org: { id: string }) => org.id === data.user.organization);
			if (currentOrg) {
				orgSearchTerm = currentOrg.name;
			}
		}
	});

	function selectOrganization(org: { id: string, name: string }) {
		selectedOrganizationId = org.id;
		orgSearchTerm = org.name;
		showSuggestions = false;
	}

	let currentLocale = $state($locale);

	locale.subscribe(value => {
		currentLocale = value;
	});
</script>

<div class="p-6 sm:p-10 space-y-6">
	<PageHeader title="Account Settings" />

	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		<Card>
			<h2 class="text-xl font-semibold mb-6 text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-3">
				Profile Information
			</h2>

			<form method="POST" action="?/updateProfile" class="space-y-4" use:enhance={() => {
					return async ({ result, update }) => {
						await update();

						if (result.type === 'success') {
							window.location.reload();
						}
					};
				}}>
				<!-- Username (not editable) -->
				<FormInput
					id="username"
					name="username"
					label="Username"
					value={data.user.username}
					disabled={true}
					helpText="Username cannot be changed"
				/>

				<!-- Full Name -->
				<FormInput
					id="name"
					name="name"
					label="Full Name"
					value={data.user.name || ''}
					required
				/>

				<!-- Email -->
				<FormInput
					id="email"
					name="email"
					type="email"
					label="Email Address"
					value={data.user.email}
					required
				/>

				<!-- Organization (if not admin or developer) -->
				{#if data.user.role !== 'admin' && data.user.role !== 'developer'}
					<div class="relative">
						<FormInput
							id="orgSearchTerm"
							name="orgSearchTerm"
							label="Organization"
							placeholder="Search organizations..."
							value={orgSearchTerm}
							onChange={(e: Event) => orgSearchTerm = (e.target as HTMLInputElement).value}
							onFocus={() => showSuggestions = true}
							onBlur={() => setTimeout(() => showSuggestions = false, 200)}

							helpText="Type to search for your organization"
						/>

						{#if showSuggestions && filteredOrganizations.length > 0}
							<div
								transition:slide={{ duration: 200 }}
								class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto"
							>
								{#each filteredOrganizations as org}
									<Button
										variant="light"
										size="sm"
										onClick={() => selectOrganization(org)}
										class="w-full text-left justify-start"
									>
										{org.name || ''}
									</Button>
								{/each}
							</div>
						{/if}

						<input type="hidden" name="organization" value={selectedOrganizationId} required />
					</div>
				{/if}

				<div class="pt-4">
					<Button
						type="submit"
						variant="primary"
					>
						Save Changes
					</Button>
				</div>

				{#if form?.message}
					<AlertMessage
						type={form.success ? "success" : "error"}
						message={form.message}
					/>
				{/if}
			</form>
		</Card>

	</div>

	<Card>
		<h2 class="text-xl font-semibold mb-6 text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-3">
			Account Security
		</h2>

		<form method="POST" action="?/changePassword" class="space-y-4" use:enhance={() => {
				return async ({ result, update }) => {
					// Run the default action
					await update();

					// Clear form fields and reload page if successful
					if (result.type === 'success' && result.data?.passwordSuccess) {
						const form = document.querySelector('form[action="?/changePassword"]') as HTMLFormElement;
						if (form) {
							form.reset();
						}
					}
				};
			}}>
			<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
				<!-- Current Password -->
				<FormInput
					id="currentPassword"
					name="currentPassword"
					type="password"
					label="Current Password"
					required
				/>

				<!-- New Password -->
				<FormInput
					id="newPassword"
					name="newPassword"
					type="password"
					label="New Password"
					minlength={6}
					required
				/>

				<!-- Confirm New Password -->
				<FormInput
					id="confirmPassword"
					name="confirmPassword"
					type="password"
					label="Confirm New Password"
					minlength={6}
					required
				/>
			</div>

			<div class="pt-4">
				<Button
					type="submit"
					variant="primary"
				>
					Update Password
				</Button>
			</div>

			{#if form?.passwordMessage}
				<AlertMessage
					type={form.passwordSuccess ? "success" : "error"}
					message={form.passwordMessage}
				/>
			{/if}
		</form>
	</Card>
</div>