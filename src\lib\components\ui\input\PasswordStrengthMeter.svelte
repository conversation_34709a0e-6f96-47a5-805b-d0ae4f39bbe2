<script lang="ts">
  import { slide } from 'svelte/transition';
  import { onMount, onDestroy } from 'svelte';
  import { checkPasswordStrength } from '$lib/utils/validation';

  const {
    password = '',
    showRequirements = false,
    onFocus = undefined,
    onBlur = undefined
  } = $props<{
    password: string,
    showRequirements?: boolean,
    onFocus?: () => void,
    onBlur?: () => void
  }>();

  let passwordStrength = $state(0);
  let isCheckingPassword = $state(false);
  let isPwned = $state(false);
  let passwordCheckTimeout: ReturnType<typeof setTimeout> | null = null;

  function updatePasswordStrength(pw: string) {
    if (!pw) {
      passwordStrength = 0;
      isPwned = false;
      return;
    }

    // Use the shared password strength checker
    passwordStrength = checkPasswordStrength(pw);

    if (passwordCheckTimeout) {
      clearTimeout(passwordCheckTimeout);
    }

    if (pw.length >= 5) {
      passwordCheckTimeout = setTimeout(() => {
        checkHaveIBeenPwned(pw);
      }, 800);
    } else {
      isPwned = false;
      isCheckingPassword = false;
    }
  }

  async function checkHaveIBeenPwned(pw: string) {
    try {
      if (!pw || pw.length < 5) {
        isPwned = false;
        return;
      }

      isCheckingPassword = true;

      // Generate SHA-1 hash
      const hashHex = await crypto.subtle.digest('SHA-1', new TextEncoder().encode(pw))
        .then(buffer => Array.from(new Uint8Array(buffer))
          .map(b => b.toString(16).padStart(2, '0'))
          .join(''));

      const prefix = hashHex.substring(0, 5).toUpperCase();
      const suffix = hashHex.substring(5).toUpperCase();

      const response = await fetch(`https://api.pwnedpasswords.com/range/${prefix}`);

      if (!response.ok) {
        isCheckingPassword = false;
        return;
      }

      // Check if password is in the breach database
      const text = await response.text();
      isPwned = text.split('\r\n')
        .some(line => line.split(':')[0] === suffix);

      // Short passwords are always considered weak
      if (pw.length < 8) {
        isPwned = true;
      }

      isCheckingPassword = false;
    } catch (error) {
      console.error('Error checking password:', error);
      isCheckingPassword = false;
    }
  }

  // Update strength when password changes
  $effect(() => {
    updatePasswordStrength(password);
  });

  onMount(() => {
    return () => {
      if (passwordCheckTimeout) {
        clearTimeout(passwordCheckTimeout);
      }
    };
  });

  onDestroy(() => {
    if (passwordCheckTimeout) {
      clearTimeout(passwordCheckTimeout);
    }
  });
</script>

{#if password}
  <div class="mt-2" transition:slide={{ duration: 300 }}>
    <div class="flex items-center justify-between mb-1">
      <div class="flex-grow h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden flex">
        <div
          class={`h-full transition-all duration-500 ${
            passwordStrength === 0 ? 'bg-gray-300 dark:bg-gray-600 w-0' :
            passwordStrength === 1 ? 'bg-red-500 w-1/4' :
            passwordStrength === 2 ? 'bg-orange-500 w-2/4' :
            passwordStrength === 3 ? 'bg-yellow-500 w-3/4' :
            'bg-green-500 w-full'
          }`}
        ></div>
      </div>
      <span class="ml-2 text-xs font-medium">
        {#if isCheckingPassword}
          <span class="text-blue-500">Checking...</span>
        {:else if passwordStrength === 0}
          <span class="text-gray-500">Password strength</span>
        {:else if passwordStrength === 1}
          <span class="text-red-500">Very weak</span>
        {:else if passwordStrength === 2}
          <span class="text-orange-500">Weak</span>
        {:else if passwordStrength === 3}
          <span class="text-yellow-600">Medium</span>
        {:else}
          <span class="text-green-500">Strong</span>
        {/if}
      </span>
    </div>

    {#if isPwned}
      <div
        transition:slide={{ duration: 200 }}
        class="text-xs text-red-500 mt-1 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800/30"
      >
        {#if password.length < 8}
          ⚠️ Password is too short. It must be at least 8 characters long.
        {:else}
          ⚠️ This password has appeared in data breaches or is too common. Please choose a different password.
        {/if}
      </div>
    {/if}

    {#if isCheckingPassword}
      <div
        transition:slide={{ duration: 200 }}
        class="text-xs text-blue-500 mt-1 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800/30 flex items-center"
      >
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Checking password security...
      </div>
    {/if}
  </div>
{/if}

{#if showRequirements}
  <div
    transition:slide={{ duration: 300 }}
    class="mt-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600"
  >
    <p class="font-medium text-sm text-gray-700 dark:text-gray-300 mb-2">Password should:</p>
    <ul class="list-disc pl-5 space-y-1 text-sm text-gray-600 dark:text-gray-400">
      <li class={password.length >= 8 ? "text-green-600 dark:text-green-400" : ""}>
        Be at least 8 characters long
        {#if password.length >= 8}<span class="text-green-600 dark:text-green-400">✓</span>{/if}
      </li>
      <li class={/[A-Z]/.test(password) && /[a-z]/.test(password) ? "text-green-600 dark:text-green-400" : ""}>
        Include both uppercase and lowercase letters
        {#if /[A-Z]/.test(password) && /[a-z]/.test(password)}<span class="text-green-600 dark:text-green-400">✓</span>{/if}
      </li>
      <li class={/[0-9]/.test(password) ? "text-green-600 dark:text-green-400" : ""}>
        Include at least one number
        {#if /[0-9]/.test(password)}<span class="text-green-600 dark:text-green-400">✓</span>{/if}
      </li>
      <li class={/[^A-Za-z0-9]/.test(password) ? "text-green-600 dark:text-green-400" : ""}>
        Include at least one special character
        {#if /[^A-Za-z0-9]/.test(password)}<span class="text-green-600 dark:text-green-400">✓</span>{/if}
      </li>
      <li class={!isPwned && password.length >= 5 ? "text-green-600 dark:text-green-400" : ""}>
        Not be a commonly used password
        {#if !isPwned && password.length >= 5}<span class="text-green-600 dark:text-green-400">✓</span>{/if}
      </li>
    </ul>
  </div>
{/if}
