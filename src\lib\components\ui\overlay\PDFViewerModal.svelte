<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { fade } from 'svelte/transition';
  import { browser } from '$app/environment';
  import ManualTableEntryModal from './ManualTableEntryModal.svelte';
  import { calculateComplexity } from '$lib/utils/complexityCalculator';

  let {
    show = false,
    url = '',
    title = '',
    submissionId = null,
    isAnalyzing = false,
    complexityData = null,
    userRole = 'student',
    onclose = () => {},
    onanalyze = () => {},
    onmanualsubmit = () => {}
  } = $props<{
    show: boolean;
    url: string;
    title: string;
    submissionId?: string | null;
    isAnalyzing?: boolean;
    complexityData?: any;
    userRole?: string;
    onclose?: () => void;
    onanalyze?: (detail: { submissionId: string }) => void;
    onmanualsubmit?: (detail: { submissionId: string, tableData: { depth: number[], time: number[] } }) => void;
  }>();

  let showManualEntryModal = $state(false);
  let analysisError = $state('');

  function handleClose() { onclose(); }
  function openInNewTab() { if (browser && url) window.open(url, '_blank'); }
  function handleBackdropClick(event: MouseEvent) { if (event.target === event.currentTarget) handleClose(); }
  function handleKeydown(event: KeyboardEvent) { if (event.key === 'Escape' && show) handleClose(); }

  function handleManualSubmit(data: { submissionId: string, tableData: { depth: number[], time: number[] } }) {
    isAnalyzing = true;
    analysisError = '';
    onmanualsubmit(data);
    showManualEntryModal = false;
  }

  function showManualEntry() {
    if (userRole !== 'lecturer') {
      showManualEntryModal = true;
    }
  }

  onMount(() => {
    if (browser) {
      window.addEventListener('keydown', handleKeydown);
      if (show) document.body.style.overflow = 'hidden';
    }

    // Automatically analyze complexity if submission ID is provided and no complexity data exists
    // Only for lecturers
    if (userRole === 'lecturer' && submissionId && !complexityData && !isAnalyzing) {
      analysisError = '';
      onanalyze({ submissionId });
    }
  });

  onDestroy(() => {
    if (browser) {
      window.removeEventListener('keydown', handleKeydown);
      document.body.style.overflow = '';
    }
  });

  $effect(() => {
    if (browser) document.body.style.overflow = show ? 'hidden' : '';
  });

  // Trigger analysis on submission ID change
  $effect(() => {
    if (userRole === 'lecturer' && submissionId && !complexityData && !isAnalyzing && show) {
      analysisError = '';
      onanalyze({ submissionId });
    }
  });

  let complexityAnalysis = $state<any>(null);

  $effect(() => {
    if (complexityData) {
      console.log('Complexity data received in PDFViewerModal:', complexityData);
      analysisError = '';
      isAnalyzing = false;

      const tables = complexityData.tables ||
                    (complexityData.data && complexityData.data.tables) ||
                    [];

      if (tables.length === 0 && !showManualEntryModal && userRole !== 'lecturer') {
        showManualEntry();
      } else if (tables.length > 0) {
        if (tables[0].time && tables[0].depth) {
          complexityAnalysis = calculateComplexity(tables[0].time, tables[0].depth);
        } else {
          console.warn('Table found but missing time or depth values:', tables[0]);
        }
      }
    }
  });
</script>

{#if show}
  <div class="fixed inset-0 z-50 flex items-center justify-center overflow-hidden" transition:fade={{ duration: 200 }}>
    <div class="fixed inset-0 bg-black bg-opacity-100" onclick={handleBackdropClick} tabindex="-1" role="presentation"></div>

    <div class="fixed inset-0 m-4 max-w-screen-xl max-h-screen bg-white dark:bg-gray-800 rounded-xl shadow-2xl flex flex-col z-10 overflow-hidden"
         role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="0">

      <div class="flex justify-between items-center py-1 px-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 rounded-t-xl">
        <div class="flex items-center space-x-4">
          <div>
            <div class="flex items-center">
              <h3 id="modal-title" class="text-lg font-medium text-gray-900 dark:text-gray-100">{title || 'PDF Viewer'}</h3>
              <button class="ml-2 inline-flex items-center px-2 py-1 text-xs font-medium rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
                      onclick={openInNewTab} title="Open in new tab">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open
              </button>
            </div>

            {#if userRole === 'lecturer' && complexityAnalysis}
              <div class="mt-1 text-sm font-medium text-blue-600 dark:text-blue-400">
                Complexity: <span class="font-bold">{complexityAnalysis.notation}</span> - {complexityAnalysis.description}
                {#if complexityAnalysis.rSquared > 0}
                  <span class="text-xs ml-1">(R² = {complexityAnalysis.rSquared.toFixed(4)})</span>
                {/if}
              </div>
            {:else if userRole === 'lecturer' && complexityData?.tables?.length > 0}
              {#each complexityData.tables as table, i}
                {#if i === 0 && table.time?.length > 0 && table.depth?.length > 0}
                  <div class="mt-1 text-sm font-medium text-blue-600 dark:text-blue-400">
                    Complexity:
                    {#if Math.max(...table.depth) / Math.max(...table.time) < 1}
                      O(log n) - Logarithmic
                    {:else if Math.max(...table.depth) / Math.max(...table.time) < 2}
                      O(n) - Linear
                    {:else if Math.max(...table.depth) / Math.max(...table.time) < 4}
                      O(n log n) - Linearithmic
                    {:else if Math.max(...table.depth) / Math.max(...table.time) < 10}
                      O(n²) - Quadratic
                    {:else}
                      O(2^n) - Exponential
                    {/if}
                  </div>
                {/if}
              {/each}
            {:else if userRole === 'lecturer' && complexityData?.data?.tables?.length > 0}
              {#each complexityData.data.tables as table, i}
                {#if i === 0 && table.time?.length > 0 && table.depth?.length > 0}
                  <div class="mt-1 text-sm font-medium text-blue-600 dark:text-blue-400">
                    Complexity:
                    {#if Math.max(...table.depth) / Math.max(...table.time) < 1}
                      O(log n) - Logarithmic
                    {:else if Math.max(...table.depth) / Math.max(...table.time) < 2}
                      O(n) - Linear
                    {:else if Math.max(...table.depth) / Math.max(...table.time) < 4}
                      O(n log n) - Linearithmic
                    {:else if Math.max(...table.depth) / Math.max(...table.time) < 10}
                      O(n²) - Quadratic
                    {:else}
                      O(2^n) - Exponential
                    {/if}
                  </div>
                {/if}
              {/each}
            {:else if userRole === 'lecturer' && complexityData && Object.keys(complexityData).length > 0}
              <!-- Log for debugging -->
              <div class="hidden">
                {console.log('Complexity data available but no tables:', complexityData)}
              </div>
            {/if}
          </div>

          <!-- Analyze complexity button removed -->

          {#if userRole === 'lecturer' && isAnalyzing}
            <div class="flex items-center text-sm text-blue-600 dark:text-blue-400">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Analyzing...
            </div>
          {/if}

          {#if userRole === 'lecturer' && analysisError}
            <div class="flex items-center text-sm text-red-600 dark:text-red-400">
              <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
              {analysisError}
            </div>
          {/if}
        </div>

        <button class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
                onclick={handleClose} aria-label="Close PDF viewer">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="flex-1 overflow-hidden rounded-b-xl">
        {#if browser}
          <embed src={url} type="application/pdf" class="w-full h-full" style="background-color: #525659;" title={title || "PDF Viewer"} />
        {:else}
          <div class="flex flex-col items-center justify-center h-full p-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            <p class="text-gray-700 dark:text-gray-300 mb-2 text-center font-medium">PDF will load in browser</p>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

{#if userRole !== 'lecturer'}
  <ManualTableEntryModal
    show={showManualEntryModal}
    submissionId={submissionId}
    onclose={() => showManualEntryModal = false}
    onsubmit={handleManualSubmit}
  />
{/if}