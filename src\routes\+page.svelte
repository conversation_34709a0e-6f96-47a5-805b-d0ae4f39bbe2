<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/state';
  import { onMount } from 'svelte';
  import { locale, t } from '$lib/stores/locale';
  import { <PERSON><PERSON>, LanguageSelector, ThemeSwitcher } from '$lib/components/ui';

  let currentLocale = $state($locale);

  onMount(() => {
    if (page.data.user) {
      goto('/dashboard');
    }
  });

  $effect(() => {
    currentLocale = $locale;
  });

  function scrollToSection(id: string) {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }
</script>

<div class="relative overflow-hidden">
  <div class="relative bg-white dark:bg-gray-800 overflow-hidden">
    <div class="absolute top-4 w-full flex justify-between px-4 z-20">
      <div class="flex items-center pt-4">
        <LanguageSelector />
      </div>
      <div class="flex items-center pt-4">
        <ThemeSwitcher />
      </div>
    </div>
    <div class="max-w-7xl mx-auto">
      <div class="relative z-10 pb-8 bg-white dark:bg-gray-800 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
        <main class="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
          <div class="sm:text-center lg:text-left">
            <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white sm:text-5xl md:text-6xl">
              <span class="block xl:inline">{t('home.securePdf', currentLocale)}</span>
              <span class="block text-indigo-600 dark:text-indigo-400 xl:inline"> {t('home.forEducation', currentLocale)}</span>
            </h1>

            <div class="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
              <div class="rounded-md shadow">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => window.location.href = '/auth/login'}
                  class="w-full md:py-4 md:text-lg md:px-10"
                >
                  {t('home.getStarted', currentLocale)}
                </Button>
              </div>
              <div class="mt-3 sm:mt-0 sm:ml-3">
                <Button
                  variant="light"
                  size="lg"
                  onClick={() => scrollToSection('features')}
                  class="w-full md:py-4 md:text-lg md:px-10"
                >
                  {t('home.learnMore', currentLocale)}
                </Button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </div>

  <div class="bg-indigo-700 dark:bg-indigo-800">
    <div class="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-extrabold text-white sm:text-4xl">
        <span class="block">{t('home.readyToStart', currentLocale)}</span>
        <span class="block">{t('home.signUpToday', currentLocale)}</span>
      </h2>
      <Button
        variant="light"
        size="lg"
        onClick={() => window.location.href = '/auth/register'}
        class="mt-8"
      >
        {t('auth.register', currentLocale)}
      </Button>
    </div>
  </div>
</div>