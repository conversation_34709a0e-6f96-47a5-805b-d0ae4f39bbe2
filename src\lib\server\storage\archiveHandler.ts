import fs from 'fs';
import path from 'path';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import crypto from 'crypto';
import { ensureUserUploadDir } from './fileStorage';
import { deleteDirectory } from './archiveExtractor';

export const EXCLUDED_DIRECTORIES = [
  '__MACOSX',
  '.git',
  'bin',
  'obj',
  'node_modules'
];

export const EXCLUDED_FILE_PATTERNS = [
  /^\._/
];

export async function extractCsFilesFromArchive(
  buffer: Buffer,
  fileExt: string,
  userId: string
): Promise<{ name: string; path: string; content: string }[]> {
  // Create temporary directory
  const tempDir = path.join(process.cwd(), 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  // Create a temporary file to extract from
  const tempFilePath = path.join(tempDir, `${crypto.randomUUID()}${fileExt}`);
  fs.writeFileSync(tempFilePath, buffer);

  // Create extraction directory
  const extractDir = path.join(ensureUserUploadDir(userId), 'extract_' + Date.now());
  if (!fs.existsSync(extractDir)) {
    fs.mkdirSync(extractDir, { recursive: true });
  }

  // Extract archive
  try {
    console.log(`Extracting archive from ${tempFilePath}`);

    // Check if the file exists and has content
    if (!fs.existsSync(tempFilePath)) {
      throw new Error(`Temporary file does not exist: ${tempFilePath}`);
    }

    const stats = fs.statSync(tempFilePath);
    if (stats.size === 0) {
      throw new Error(`Temporary file is empty: ${tempFilePath}`);
    }

    console.log(`Reading archive file: ${tempFilePath} (${stats.size} bytes)`);

    const zipBuffer = fs.readFileSync(tempFilePath);
    console.log(`Archive file read successfully (${zipBuffer.length} bytes)`);

    const zip = await JSZip.loadAsync(zipBuffer, { checkCRC32: false });
    console.log('Archive loaded successfully');

    let totalFiles = 0;
    let csFilesCount = 0;

    zip.forEach((relativePath, zipEntry) => {
      if (!zipEntry.dir) {
        totalFiles++;
        if (relativePath.toLowerCase().endsWith('.cs')) {
          csFilesCount++;
        }
      }
    });

    console.log(`Archive contains ${totalFiles} files, ${csFilesCount} CS files`);

    if (csFilesCount === 0) {
      console.warn('No CS files found in the archive');
    }

    const extractPromises: Promise<void>[] = [];
    let extractedCount = 0;

    zip.forEach((relativePath, zipEntry) => {
      // Skip directories and not CS files
      if (zipEntry.dir || !relativePath.toLowerCase().endsWith('.cs')) {
        return;
      }

      // Skip excluded files and directories
      if (shouldExcludeFile(relativePath)) {
        console.log(`Skipping excluded file: ${relativePath}`);
        return;
      }

      const targetPath = path.join(extractDir, relativePath);
      const targetDir = path.dirname(targetPath);

      // Create directory if it doesn't exist
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // Extract file
      const promise = zipEntry.async('nodebuffer').then(content => {
        fs.writeFileSync(targetPath, content);
        extractedCount++;
        console.log(`Extracted file: ${relativePath} (${content.length} bytes)`);
      }).catch(err => {
        console.error(`Error extracting ${relativePath}:`, err);
      });

      extractPromises.push(promise);
    });

    // Wait for all extractions to complete
    await Promise.all(extractPromises);
    console.log(`Successfully extracted ${extractedCount} CS files`);

  } catch (error) {
    console.error('Error extracting archive:', error);

    try {
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
        console.log(`Cleaned up temporary file: ${tempFilePath}`);
      }

      if (fs.existsSync(extractDir)) {
        await deleteDirectory(extractDir);
        console.log(`Cleaned up extraction directory: ${extractDir}`);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up temporary files:', cleanupError);
    }

    throw error;
  }

  // Clean up temporary file
  try {
    fs.unlinkSync(tempFilePath);
  } catch (cleanupError) {
    console.error('Error cleaning up temporary file:', cleanupError);
  }

  // Process extracted CS files
  const csFiles: { name: string; path: string; content: string }[] = [];
  findCsFiles(extractDir, csFiles);

  return csFiles;
}

function shouldExcludeFile(filePath: string): boolean {
  for (const dir of EXCLUDED_DIRECTORIES) {
    if (filePath.includes(`/${dir}/`) || filePath.includes(`\\${dir}\\`)) {
      return true;
    }
  }

  const fileName = path.basename(filePath);
  for (const pattern of EXCLUDED_FILE_PATTERNS) {
    if (pattern.test(fileName)) {
      return true;
    }
  }

  return false;
}

function findCsFiles(dir: string, results: { name: string; path: string; content: string }[], baseDir?: string): void {
  const rootDir = baseDir || dir;

  try {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);

      try {
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          if (EXCLUDED_DIRECTORIES.includes(file)) {
            continue;
          }

          findCsFiles(filePath, results, rootDir);
        } else if (path.extname(file).toLowerCase() === '.cs' && !EXCLUDED_FILE_PATTERNS.some(p => p.test(file))) {
          try {
            let content = fs.readFileSync(filePath, 'utf8');

            content = content.replace(/\0/g, '')
              .replace(/[^\x20-\x7E\x0A\x0D\xA0-\xFF]/g, '');

            results.push({
              name: file,
              path: path.relative(rootDir, filePath),
              content
            });
          } catch (error) {
            console.error(`Error reading CS file ${filePath}:`, error);
          }
        }
      } catch (statError) {
        console.error(`Error accessing file ${filePath}:`, statError);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }
}

export async function createCsFilesZip(
  csFiles: Array<{ name: string; path?: string; content: string }>
): Promise<Buffer> {
  console.log(`Creating ZIP file from ${csFiles.length} CS files`);

  try {
    const zip = new JSZip();
    let filesAdded = 0;

    for (const csFile of csFiles) {
      try {
        if (!csFile.content) {
          console.warn(`Skipping file with empty content: ${csFile.name}`);
          continue;
        }

        const fileName = csFile.path ? csFile.path.replace(/\\/g, '/') : csFile.name;
        zip.file(`${fileName}.txt`, csFile.content);
        filesAdded++;
        console.log(`Added file to ZIP: ${fileName}.txt (${csFile.content.length} bytes)`);
      } catch (error) {
        console.error(`Error adding file ${csFile.name} to ZIP:`, error);
      }
    }

    console.log(`Total files added to ZIP: ${filesAdded}`);

    if (filesAdded === 0) {
      console.warn('No files were added to ZIP, adding sample file');
      zip.file('sample.cs.txt', 'using System;\n\nclass Program\n{\n    static void Main()\n    {\n        Console.WriteLine("No CS files were found.");\n    }\n}');
    }

    console.log('Generating ZIP file...');
    const zipContent = await zip.generateAsync({
      type: 'nodebuffer',
      compression: 'DEFLATE',
      compressionOptions: { level: 6 }
    });

    console.log(`ZIP file generated successfully (${zipContent.length} bytes)`);

    if (zipContent.length === 0) {
      throw new Error('Generated ZIP file is empty');
    }

    return zipContent;
  } catch (error) {
    console.error('Error creating ZIP file:', error);
    throw error;
  }
}
