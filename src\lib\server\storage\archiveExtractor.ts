import fs from 'fs';
import path from 'path';
import { ensureUserUploadDir } from './fileStorage';
import AdmZ<PERSON> from 'adm-zip';
import Seven from 'node-7z';
import { createExtractorFromData } from 'node-unrar-js';

const EXCLUDED_DIRS = [
  '.vs', 'bin', 'obj', 'node_modules', 'packages',
  '.git', '__macosx', 'debug', 'release',
  'properties', '.idea', '.vscode', 'build', 'dist',
  'target', 'out', 'generated', 'temp', 'tmp'
];
const SUPPORTED_EXTS = ['.pdf', '.cs'];

// Security limits for archive extraction
const MAX_EXTRACTION_SIZE = 100 * 1024 * 1024; // 100MB total extraction limit
const MAX_COMPRESSION_RATIO = 100; // Maximum compression ratio to detect zip bombs
const MAX_NESTED_DEPTH = 3; // Maximum nested archive depth
const MAX_FILES_PER_ARCHIVE = 1000; // Maximum number of files per archive

interface ExtractedFiles {
  pdfFile?: { path: string; name: string; size: number; };
  csFiles: { content: string; name: string; path: string; }[];
  extractDir: string;
}

interface ExtractionContext {
  totalExtractedSize: number;
  fileCount: number;
  nestedDepth: number;
}

/**
 * Validates archive entry for security threats
 */
function validateArchiveEntry(entryName: string, compressedSize: number, uncompressedSize: number): void {
  // Check for directory traversal
  const normalizedPath = path.normalize(entryName);
  if (normalizedPath.includes('..') || path.isAbsolute(normalizedPath)) {
    throw new Error(`Potential directory traversal detected: ${entryName}`);
  }

  // Check compression ratio for zip bomb detection
  if (compressedSize > 0 && uncompressedSize / compressedSize > MAX_COMPRESSION_RATIO) {
    throw new Error(`Suspicious compression ratio detected: ${uncompressedSize / compressedSize}`);
  }

  // Check individual file size
  if (uncompressedSize > MAX_EXTRACTION_SIZE / 10) { // Single file shouldn't be more than 10% of total limit
    throw new Error(`File too large: ${uncompressedSize} bytes`);
  }
}

/**
 * Updates and validates extraction context
 */
function updateExtractionContext(context: ExtractionContext, fileSize: number): void {
  context.totalExtractedSize += fileSize;
  context.fileCount++;

  if (context.totalExtractedSize > MAX_EXTRACTION_SIZE) {
    throw new Error(`Total extraction size limit exceeded: ${context.totalExtractedSize} bytes`);
  }

  if (context.fileCount > MAX_FILES_PER_ARCHIVE) {
    throw new Error(`Too many files in archive: ${context.fileCount}`);
  }
}

/**
 * Sanitizes file path to prevent directory traversal
 */
function sanitizeFilePath(filePath: string): string {
  // Remove any path traversal attempts
  const sanitized = path.normalize(filePath)
    .replace(/\.\./g, '')
    .replace(/^[\/\\]+/, '');

  return sanitized;
}

function isExcludedPath(filePath: string): boolean {
  const normalizedPath = filePath.toLowerCase();
  const parts = normalizedPath.split(/[\/\\]/);

  if (parts.some(part => EXCLUDED_DIRS.includes(part))) {
    return true;
  }

  if (normalizedPath.includes('__macosx') || normalizedPath.includes('.ds_store')) {
    return true;
  }

  const autoGenPatterns = [
    /\.git/i, /\.svn/i, /\.hg/i, /\.vs/i, /\.vscode/i, /\.idea/i,
    /obj\/debug/i, /obj\/release/i, /bin\/debug/i, /bin\/release/i,
    /node_modules/i, /packages/i, /bower_components/i,
    /\.nuget/i, /\.gradle/i, /\.mvn/i, /target\/classes/i,
    /build\/tmp/i, /dist\/build/i, /out\/production/i
  ];

  if (autoGenPatterns.some(pattern => pattern.test(normalizedPath))) {
    return true;
  }

  return false;
}

function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

export async function deleteDirectory(dirPath: string): Promise<void> {
  if (!fs.existsSync(dirPath)) return;

  try {
    for (const entry of fs.readdirSync(dirPath)) {
      const entryPath = path.join(dirPath, entry);

      if (fs.statSync(entryPath).isDirectory()) {
        await deleteDirectory(entryPath);
      } else {
        fs.unlinkSync(entryPath);
      }
    }

    fs.rmdirSync(dirPath);
  } catch (error) {
    console.error(`Error deleting directory ${dirPath}:`, error);
  }
}

async function findFiles(dir: string, result: ExtractedFiles): Promise<void> {
  try {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);

      try {
        const stat = fs.statSync(filePath);

        // Handle directories recursively
        if (stat.isDirectory()) {
          if (!isExcludedPath(filePath)) {
            await findFiles(filePath, result);
          }
          continue;
        }

        // Skip macOS
        const fileName = path.basename(file);
        if (fileName.startsWith('._') || filePath.includes('__MACOSX')) {
          continue;
        }

        const ext = path.extname(file).toLowerCase();

        // Handle archives
        if (['.zip', '.rar', '.7z'].includes(ext)) {
          console.log(`Found nested archive: ${filePath}`);

          const nestedDir = path.join(path.dirname(filePath), `_nested_${path.basename(file, ext)}`);
          ensureDir(nestedDir);

          try {
            // Extract archive
            if (ext === '.zip') {
              await extractZip(filePath, nestedDir);
            } else if (ext === '.rar') {
              await extractRar(filePath, nestedDir);
            } else if (ext === '.7z') {
              await extract7z(filePath, nestedDir);
            }

            // Process the extracted files
            await findFiles(nestedDir, result);
          } catch (nestedError) {
            console.error(`Error processing nested archive ${filePath}:`, nestedError);
          }
        } else if (ext === '.pdf') {
          if (!result.pdfFile || stat.size > result.pdfFile.size) {
            result.pdfFile = {
              path: filePath,
              name: fileName,
              size: stat.size
            };
          }
        } else if (ext === '.cs') {
          try {
            // Try to read the file as UTF-8 text
            let content;
            try {
              content = fs.readFileSync(filePath, 'utf8');
            } catch {
              // Fallback to binary reading and cleanup
              const buffer = fs.readFileSync(filePath);
              content = buffer.toString('utf8')
                .replace(/\0/g, '')
                .replace(/[^\x20-\x7E\x0A\x0D\xA0-\xFF]/g, '');
            }

            // Add CS file to results
            result.csFiles.push({
              content,
              name: fileName,
              path: path.relative(result.extractDir, filePath)
            });
          } catch (error) {
            console.error(`Error processing CS file ${file}:`, error);
          }
        }
      } catch (statError) {
        console.error(`Error accessing file ${filePath}:`, statError);
      }
    }
  } catch (dirError) {
    console.error(`Error reading directory ${dir}:`, dirError);
  }
}

async function extractZip(archivePath: string, extractDir: string, context: ExtractionContext = { totalExtractedSize: 0, fileCount: 0, nestedDepth: 0 }): Promise<void> {
  try {
    const zip = new AdmZip(archivePath);
    const entries = zip.getEntries();

    // Check total number of entries
    if (entries.length > MAX_FILES_PER_ARCHIVE) {
      throw new Error(`Archive contains too many files: ${entries.length}`);
    }

    // Extract only supported file types, skipping excluded paths
    for (const entry of entries) {
      // Skip directories and excluded paths
      if (entry.isDirectory || isExcludedPath(entry.entryName)) {
        continue;
      }

      // Validate entry for security threats
      validateArchiveEntry(entry.entryName, entry.header.compressedSize, entry.header.size);

      // Only extract supported file types
      const entryExt = path.extname(entry.entryName).toLowerCase();
      if (SUPPORTED_EXTS.includes(entryExt)) {
        try {
          // Update extraction context before extracting
          updateExtractionContext(context, entry.header.size);

          // Sanitize the entry name
          const sanitizedPath = sanitizeFilePath(entry.entryName);
          const targetPath = path.join(extractDir, sanitizedPath);

          // Ensure target path is within extraction directory
          const resolvedTarget = path.resolve(targetPath);
          const resolvedExtractDir = path.resolve(extractDir);
          if (!resolvedTarget.startsWith(resolvedExtractDir)) {
            throw new Error(`Path traversal attempt detected: ${entry.entryName}`);
          }

          zip.extractEntryTo(entry, extractDir, false, true);
        } catch (extractError) {
          console.error(`Error extracting entry ${entry.entryName}:`, extractError);
          // Continue with other entries even if one fails
        }
      }
    }
  } catch (error) {
    console.error('Error processing ZIP archive:', error);

    // Try to extract using 7z
    try {
      console.log('Attempting to extract ZIP using 7z fallback method...');
      await extract7z(archivePath, extractDir);
    } catch (fallbackError) {
      console.error('7z fallback extraction failed:', fallbackError);
      throw new Error('Failed to extract ZIP archive: The file may be corrupted or in an unsupported format.');
    }
  }
}

async function extract7z(archivePath: string, extractDir: string): Promise<void> {
  const tempDir = path.join(extractDir, '_temp_7z_extract');
  ensureDir(tempDir);

  await Seven.extractFull(archivePath, tempDir, {
    $progress: true,
    $cherryPick: ['*.pdf', '*.cs']
  });

  // Copy files from temp dir to extract dir, skipping excluded directories
  const processDir = (dir: string) => {
    for (const file of fs.readdirSync(dir)) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        if (!EXCLUDED_DIRS.includes(file.toLowerCase())) {
          processDir(filePath);
        }
      } else {
        const ext = path.extname(file).toLowerCase();
        if (SUPPORTED_EXTS.includes(ext)) {
          const relativePath = path.relative(tempDir, filePath);
          const targetPath = path.join(extractDir, relativePath);
          ensureDir(path.dirname(targetPath));
          fs.copyFileSync(filePath, targetPath);
        }
      }
    }
  };

  processDir(tempDir);
  await deleteDirectory(tempDir);
}

async function extractRar(archivePath: string, extractDir: string): Promise<void> {
  try {
    console.log(`Extracting RAR archive: ${archivePath} to ${extractDir}`);
    const rarBuffer = fs.readFileSync(archivePath);

    // Create extractor from the RAR file data
    const extractor = await createExtractorFromData({
      data: rarBuffer
    });

    // Extract all files
    const extracted = extractor.extract({
      files: [] // This will extract all files
    });

    // Convert the iterator to an array to process it
    const files = [...extracted.files];

    let extractedAnyFile = false;

    // Process the extracted files
    for (const file of files) {
      if (!file.extraction || !file.fileHeader) continue;

      const fileName = file.fileHeader.name;

      try {
        // Skip files in excluded directories
        if (isExcludedPath(fileName)) {
          continue;
        }

        // Handle paths with directories
        const filePath = path.join(extractDir, fileName.replace(/\\/g, '/'));
        ensureDir(path.dirname(filePath));

        // Write the extracted file
        try {
          fs.writeFileSync(filePath, Buffer.from(file.extraction));
          extractedAnyFile = true;
        } catch (writeError) {
          console.error(`Error writing file ${filePath}:`, writeError);
        }
      } catch (extractError) {
        console.error(`Error extracting file ${fileName}:`, extractError);
      }
    }

    if (!extractedAnyFile) {
      // Try a fallback approach with 7z
      try {
        console.log(`Attempting to extract RAR using 7z fallback method...`);
        await extract7z(archivePath, extractDir);
      } catch (fallbackError) {
        console.error(`7z fallback extraction failed:`, fallbackError);
        throw new Error('Failed to extract RAR archive');
      }
    }
  } catch (error) {
    console.error(`Error extracting RAR archive:`, error);
    throw error;
  }
}

export async function extractArchive(archivePath: string, userId: string, requirePdf: boolean = true): Promise<ExtractedFiles> {
  // Create extraction directory
  const extractDir = path.join(ensureUserUploadDir(userId), 'extract_' + Date.now());
  ensureDir(extractDir);

  // Initialize result object
  const extractedFiles: ExtractedFiles = {
    extractDir,
    csFiles: []
  };

  try {
    const ext = path.extname(archivePath).toLowerCase();

    if (ext === '.pdf') {
      const fileName = path.basename(archivePath);
      const destPath = path.join(extractDir, fileName);

      // Copy the PDF file to extraction directory
      fs.copyFileSync(archivePath, destPath);
      const fileSize = fs.statSync(destPath).size;

      extractedFiles.pdfFile = {
        path: destPath,
        name: fileName,
        size: fileSize
      };

      return extractedFiles;
    }

    // Extract archives based on file extension
    switch (ext) {
      case '.zip':
        await extractZip(archivePath, extractDir);
        break;
      case '.rar':
        await extractRar(archivePath, extractDir);
        break;
      case '.7z':
        await extract7z(archivePath, extractDir);
        break;
      default:
        throw new Error(`Unsupported archive format: ${ext}`);
    }

    await findFiles(extractDir, extractedFiles);

    if (requirePdf && !extractedFiles.pdfFile) {
      const foundPdf = findPdfFile(extractDir, extractedFiles);

      if (!foundPdf) {
        throw new Error('No PDF file found in the archive');
      }
    }

    return extractedFiles;
  } catch (error) {
    // Clean up extraction directory on error
    try {
      if (fs.existsSync(extractDir)) {
        await deleteDirectory(extractDir);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up directory:', cleanupError);
    }
    throw error;
  }
}

function findPdfFile(dir: string, result: ExtractedFiles): boolean {
  try {
    for (const file of fs.readdirSync(dir)) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory() && !isExcludedPath(filePath)) {
        if (findPdfFile(filePath, result)) return true;
      } else if (path.extname(file).toLowerCase() === '.pdf') {
        // Found a PDF file, add it to the results
        result.pdfFile = {
          path: filePath,
          name: path.basename(file),
          size: stat.size
        };
        return true;
      }
    }
  } catch (error) {
    console.error(`Error in findPdfFile: ${error}`);
  }

  return false;
}

export async function batchProcessArchive(archivePath: string, userId: string): Promise<{
  csFiles: { content: string; name: string; path: string; }[];
  extractDir: string;
}> {
  try {
    const extractedFiles = await extractArchive(archivePath, userId, false);

    // Filter out any problematic CS files
    const validCsFiles = extractedFiles.csFiles.filter(file => {
      if (file.path.includes('__MACOSX') || file.name.startsWith('._')) {
        console.log(`Skipping macOS system file: ${file.path}`);
        return false;
      }

      try {
        Buffer.from(file.content).toString('utf8');
        return true;
      } catch (error) {
        console.error(`Invalid UTF-8 encoding: ${file.path}`);
        return false;
      }
    });

    return {
      csFiles: validCsFiles,
      extractDir: extractedFiles.extractDir
    };
  } catch (error) {
    console.error('Error in batch processing:', error);
    throw error;
  }
}