<script lang="ts">
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import type { ActionData } from './$types';
	import { locale, t } from '$lib/stores/locale';
	import {
		Button,
		AuthLayout,
		FormInput,
		FormSelect,
		AlertMessage,
		PasswordStrengthMeter
	} from '$lib/components/ui';

	let { form, data }: { form: ActionData, data: any } = $props();

	let showPasswordRequirements = $state(false);
	let password = $state('');
	let selectedOrganizationId = $state('');
	let selectedRole = $state('student');
	let currentLocale = $state($locale);

	locale.subscribe(value => {
		currentLocale = value;
	});

	// Create organization options from data
	const organizationOptions = $derived(
		data?.organizations?.map((org: any) => ({
			value: org.id,
			label: org.name
		})) || []
	);

	// Create role options
	const roleOptions = [
		{ value: 'student', label: 'Student' },
		{ value: 'lecturer', label: 'Lecturer' }
	];

	function handleSubmit() {
		return async ({ result, update }: { result: any, update: () => Promise<void> }) => {
			await update();

			if (result.type === 'success' && result.data?.redirectTo) {
				goto(result.data.redirectTo);
			}
		};
	}
</script>

<AuthLayout title={t('auth.createAccount', currentLocale)}>
	<form class="mt-10 space-y-8" method="POST" action="?/register" use:enhance={handleSubmit}>
		<div class="space-y-6">
			<FormInput
				id="username"
				name="username"
				type="text"
				label={t('auth.username', currentLocale)}
				placeholder={t('auth.username', currentLocale)}
				required
			/>

			<FormInput
				id="name"
				name="name"
				type="text"
				label="Full Name"
				placeholder="Enter your full name"
				required
			/>

			<FormInput
				id="email"
				name="email"
				type="email"
				label={t('auth.email', currentLocale)}
				placeholder={t('auth.email', currentLocale)}
				required
			/>

			<div>
				<FormInput
					id="password"
					name="password"
					type="password"
					label={t('auth.password', currentLocale)}
					value={password}
					onChange={(e: Event) => {
						password = (e.target as HTMLInputElement).value;
						showPasswordRequirements = true;
					}}
					onBlur={() => showPasswordRequirements = false}
					placeholder={t('auth.password', currentLocale)}
					required
				/>

				<PasswordStrengthMeter
					{password}
					showRequirements={showPasswordRequirements}
				/>
			</div>

			<FormSelect
				id="organization"
				name="organization"
				label="Organization"
				value={selectedOrganizationId}
				options={organizationOptions}
				placeholder="Select your organization"
				onChange={(e: Event) => selectedOrganizationId = (e.target as HTMLSelectElement).value}
				helpText="Select your organization from the list"
				required
			/>

			<FormSelect
				id="role"
				name="role"
				label={t('auth.accountType', currentLocale)}
				value={selectedRole}
				options={roleOptions}
				onChange={(e: Event) => selectedRole = (e.target as HTMLSelectElement).value}
				helpText={t('auth.approvalRequired', currentLocale)}
				required
			/>
		</div>

		{#if form?.message}
			<AlertMessage
				type="error"
				message={form.message}
			/>
		{/if}

		<div class="pt-2">
			<Button
				type="submit"
				variant="primary"
				size="lg"
				class="w-full"
			>
				{t('auth.register', currentLocale)}
			</Button>
		</div>

		<p class="mt-4 text-center text-base text-gray-600 dark:text-gray-400">
			<a href="/auth/login" class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
				{t('auth.existingAccount', currentLocale)}
			</a>
		</p>
	</form>
</AuthLayout>