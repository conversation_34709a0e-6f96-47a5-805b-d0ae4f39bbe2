<script lang="ts">
  import { slide } from 'svelte/transition';
  import { quintOut } from 'svelte/easing';
  import { page } from '$app/stores';

  const {
    href = '',
    label = '',
    isActive = undefined,
    class: className = '',
    children
  } = $props<{
    href: string,
    label?: string,
    isActive?: boolean,
    class?: string,
    children?: any
  }>();

  const active = $derived(
    isActive !== undefined
      ? isActive
      : $page.url.pathname === href || $page.url.pathname.startsWith(href + '/')
  );
</script>

<a
  {href}
  class="nav-link text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 {active ? 'text-indigo-600 dark:text-indigo-400' : ''} {className}"
>
  <div class="content-container">
    {#if children}
      {@render children()}
    {:else}
      <span>{label}</span>
    {/if}
    <div class="indicator-container">
      {#if active}
        <div in:slide|local={{ duration: 200, easing: quintOut }} class="indicator"></div>
      {/if}
    </div>
  </div>
</a>

<style lang="tailwind">
  .nav-link {
    height: 4rem;
    display: flex;
    align-items: center;
    position: relative;
  }

  .indicator-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    overflow: hidden;
  }

  .indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #4f46e5;
  }

  .content-container {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 4px;
  }
</style>
