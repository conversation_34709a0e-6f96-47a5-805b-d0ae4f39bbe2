<script lang="ts">
  import { browser } from '$app/environment';
  import { Modal, Button, AlertMessage } from '$lib/components/ui';
  import { ManualTableEntryModal } from '$lib/components/ui';

  let {
    show = false,
    url = '',
    title = '',
    submissionId = null,
    isAnalyzing = false,
    complexityData = null,
    onclose = () => {},
    onanalyze = () => {},
    onmanualsubmit = () => {}
  } = $props<{
    show: boolean;
    url: string;
    title: string;
    submissionId?: string | null;
    isAnalyzing?: boolean;
    complexityData?: any;
    onclose?: () => void;
    onanalyze?: (detail: { submissionId: string }) => void;
    onmanualsubmit?: (detail: { submissionId: string, tableData: { depth: number[], time: number[] } }) => void;
  }>();

  let showManualEntryModal = $state(false);
  let analysisError = $state('');

  function handleClose() { onclose(); }
  function openInNewTab() { if (browser && url) window.open(url, '_blank'); }

  function handleManualSubmit(data: { submissionId: string, tableData: { depth: number[], time: number[] } }) {
    isAnalyzing = true;
    analysisError = '';

    onmanualsubmit(data);
    showManualEntryModal = false;
  }

  function showManualEntry() {
    showManualEntryModal = true;
  }

  $effect(() => {
    if (complexityData) {
      analysisError = '';
      isAnalyzing = false;

      if (complexityData.tables && complexityData.tables.length === 0 && !showManualEntryModal) {
        showManualEntry();
      }
    }
  });
  $effect(() => {
    if (submissionId && !complexityData && !isAnalyzing && show) {
      analysisError = '';
      onanalyze({ submissionId });
    }
  });


</script>

<Modal
  {show}
  {title}
  size="full"
  onClose={handleClose}
>
  <div class="flex flex-col h-full">
    <div class="flex justify-between items-center py-1 px-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
      <div class="flex items-center space-x-4 w-full">
        <div>
          <div class="flex items-center">
            <Button
              onClick={openInNewTab}
              variant="light"
              size="sm"

              class="ml-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              Open
            </Button>
          </div>

          {#if complexityData?.tables?.length > 0}
            {#each complexityData.tables as table, i}
              {#if i === 0 && table.time?.length > 0 && table.depth?.length > 0}
                <div class="mt-1 text-sm font-medium text-blue-600 dark:text-blue-400">
                  Complexity:
                  {#if Math.max(...table.depth) / Math.max(...table.time) < 1}
                    O(log n) - Logarithmic
                  {:else if Math.max(...table.depth) / Math.max(...table.time) < 2}
                    O(n) - Linear
                  {:else if Math.max(...table.depth) / Math.max(...table.time) < 4}
                    O(n log n) - Linearithmic
                  {:else if Math.max(...table.depth) / Math.max(...table.time) < 10}
                    O(n²) - Quadratic
                  {:else}
                    O(2^n) - Exponential
                  {/if}
                </div>
              {/if}
            {/each}
          {/if}
        </div>

        <!-- Analyze complexity button removed -->

        {#if isAnalyzing}
          <div class="flex items-center text-sm text-blue-600 dark:text-blue-400">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Claude AI is analyzing your data...
          </div>
        {/if}

        {#if analysisError}
          <AlertMessage
            type="error"
            message={analysisError}
            class="text-sm"
          />
        {/if}
      </div>
    </div>

    <div class="flex-1 overflow-hidden">
      {#if browser}
        <embed src={url} type="application/pdf" class="w-full h-full" style="background-color: #525659;" title={title || "PDF Viewer"} />
      {:else}
        <div class="flex flex-col items-center justify-center h-full p-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          <p class="text-gray-700 dark:text-gray-300 mb-2 text-center font-medium">PDF will load in browser</p>
        </div>
      {/if}
    </div>
  </div>
</Modal>

<ManualTableEntryModal
  show={showManualEntryModal}
  submissionId={submissionId}
  onclose={() => showManualEntryModal = false}
  onsubmit={handleManualSubmit}
/>
