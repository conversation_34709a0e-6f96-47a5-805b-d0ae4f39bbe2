<script lang="ts">
	import { enhance } from '$app/forms';
	import { locale, t } from '$lib/stores/locale';
	import { AuthLayout, AlertMessage, Button } from '$lib/components/ui';

	let currentLocale = $state($locale);

	locale.subscribe(value => {
		currentLocale = value;
	});
</script>

<AuthLayout
	title="Account Pending Approval"
	subtitle="Your lecturer account is pending administrator approval."
	backHref="/"
	showLanguageSelector={true}
>
	<div class="mt-8 space-y-6">
		<AlertMessage
			type="warning"
			message="Your lecturer account is currently pending administrator approval. You'll receive an email notification once your account has been approved. If you think this is a mistake or have urgent questions, please contact the system administrator."
		/>

		<form method="post" action="?/logout" class="mt-6" use:enhance>
			<Button
				type="submit"
				variant="danger"
				size="lg"
				class="w-full"
			>
				{t('auth.logout', currentLocale)}
			</Button>
		</form>
	</div>
</AuthLayout>