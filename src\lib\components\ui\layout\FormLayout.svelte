<script lang="ts">
  import { Button } from '$lib/components/ui';
  import { locale, t } from '$lib/stores/locale';

  const {
    title = '',
    submitText = '',
    cancelHref = '',
    cancelText = '',
    isSubmitting = false,
    class: className = '',
    onCancel = undefined,
    children
  } = $props<{
    title?: string,
    submitText?: string,
    cancelHref?: string,
    cancelText?: string,
    isSubmitting?: boolean,
    class?: string,
    onCancel?: () => void,
    children: any
  }>();

  let currentLocale = $state($locale);

  locale.subscribe(value => {
    currentLocale = value;
  });

  const submitButtonText = $derived(submitText || t('common.save', currentLocale));
  const cancelButtonText = $derived(cancelText || t('common.cancel', currentLocale));

  const formClass = $derived(`
    bg-white dark:bg-gray-800
    rounded-lg shadow-sm p-6
    border border-gray-200 dark:border-gray-700
    ${className}
  `);
  function handleCancel() {
    if (onCancel) {
      onCancel();
    }
  }
</script>

<div class={formClass}>
  {#if title}
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{title}</h2>
  {/if}

  <div class="space-y-6">
    {@render children()}

    <div class="flex justify-end space-x-3 pt-4">
      {#if cancelHref || onCancel}
        {#if cancelHref}
          <a
            href={cancelHref}
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
          >
            {cancelButtonText}
          </a>
        {:else}
          <Button
            type="button"
            variant="default"
            onClick={handleCancel}
          >
            {cancelButtonText}
          </Button>
        {/if}
      {/if}

      <Button
        type="submit"
        variant="primary"
        disabled={isSubmitting}
      >
        {#if isSubmitting}
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {t('common.loading', currentLocale)}
        {:else}
          {submitButtonText}
        {/if}
      </Button>
    </div>
  </div>
</div>
