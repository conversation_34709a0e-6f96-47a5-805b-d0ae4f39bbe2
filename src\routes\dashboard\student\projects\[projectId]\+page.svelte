<script lang="ts">
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import {
    Card,
    Countdown,
    <PERSON><PERSON>,
    PageHeader,
    AlertMessage,
    PDFViewerModal,
    ManualTableEntryModal
  } from '$lib/components/ui';

  type FormData = {
    success?: boolean;
    message?: string;
    submitted?: boolean;
    submissionId?: string;
    analysisComplete?: boolean;
  };

  let { data, form } = $props<{ data: any, form: FormData | null }>();

  const initialFormState: FormData = {
    success: false,
    message: '',
    submitted: false,
    submissionId: '',
    analysisComplete: false
  };

  let isSubmitting = $state(false);
  let selectedFile: File | null = $state(null);
  let submissionSuccess = $state(false);
  let submissionFailed = $state(false);
  let failureMessage = $state('');
  let dragActive = $state(false);
  let fileInputRef = $state<HTMLInputElement | null>(null);
  let showManualEntryModal = $state(false);
  let currentSubmissionId = $state('');

  let showPdfViewer = $state(false);
  let currentPdfUrl = $state('');
  let currentPdfTitle = $state('');
  let isAnalyzing = $state(false);
  let complexityData = $state(null);

  const deadlinePassed = $state(data.project.deadline ? new Date() > new Date(data.project.deadline) : false);
  const submissionCount = $state(data.submissions?.length || 0);
  const attemptCount = $state(data.attemptCount || 0);
  const maxAttemptsReached = $state(data.project.maxAttempts > 0 && attemptCount >= data.project.maxAttempts);
  const canSubmit = $state(!deadlinePassed && !maxAttemptsReached);

  const deadlineApproaching = $state(() => {
    if (!data.project.deadline) return false;
    const now = new Date();
    const deadline = new Date(data.project.deadline);
    const hoursRemaining = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);
    return !deadlinePassed && hoursRemaining <= 24;
  });

  const formattedDeadline = $state(data.project.deadline ? new Date(data.project.deadline).toLocaleString() : 'No deadline');

  function viewSubmissions() {
    goto('/dashboard/student/submissions');
  }

  function handleFileChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      selectedFile = input.files[0];
    }
  }

  function viewPdf(submission: any) {
    const pathParts = submission.filePath.split('/');
    const studentId = pathParts[2]; // /uploads/studentId/filename

    currentPdfUrl = `/api/pdf/${studentId}/${pathParts.slice(3).join('/')}`;
    currentPdfTitle = submission.originalFilename;
    currentSubmissionId = submission.id;
    showPdfViewer = true;
  }

  function closePdfViewer() {
    showPdfViewer = false;
    complexityData = null;
    isAnalyzing = false;
  }

  // analyze submission
  async function analyzeSubmission(detail: { submissionId: string }) {
    if (!detail.submissionId) return;

    isAnalyzing = true;
    complexityData = null;

    try {
      const response = await fetch('/api/analyze-submission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ submissionId: detail.submissionId })
      });

      if (!response.ok) {
        throw new Error('Failed to analyze submission');
      }

      const result = await response.json();
      complexityData = result;
    } catch (error) {
      console.error('Error analyzing submission:', error);
    } finally {
      isAnalyzing = false;
    }
  }

  // manual table data submission
  async function handleManualSubmit(detail: { submissionId: string, tableData: { depth: number[], time: number[] } }) {
    if (!detail.submissionId) return;

    isAnalyzing = true;
    let errorOccurred = false;

    try {
      const response = await fetch('/api/analyze-submission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          submissionId: detail.submissionId,
          manualTableData: detail.tableData
        })
      });

      const result = await response.json();

      if (!response.ok) {
        const errorMessage = result.error || 'Failed to save manual data';
        console.error('Server error:', errorMessage);
        errorOccurred = true;

        submissionSuccess = false;
        submissionFailed = true;
        failureMessage = `Error saving manual data: ${errorMessage}`;

        form = { ...initialFormState };

        throw new Error(errorMessage);
      }

      complexityData = result;

      const submissionIndex = data.submissions.findIndex((s: any) => s.id === detail.submissionId);
      if (submissionIndex !== -1) {
        data.submissions[submissionIndex].hasComplexityAnalysis = true;
      }
    } catch (error) {
      console.error('Error saving manual data:', error);

      if (!errorOccurred) {
        submissionSuccess = false;
        submissionFailed = true;
        failureMessage = 'Failed to save manual data. Please try again.';
      }

      isAnalyzing = false;
    } finally {
      if (!complexityData) {
        isAnalyzing = false;
      }
    }
  }

  // delete a submission and show failure message
  async function deleteSubmission(submissionId: string) {
    try {
      const response = await fetch(`/api/submissions/${submissionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        console.error('Failed to delete submission');
      }

      submissionSuccess = false;
      submissionFailed = true;
      failureMessage = 'Submission failed. Manual data entry was cancelled.';

      form = null;
      currentSubmissionId = '';
    } catch (error) {
      console.error('Error deleting submission:', error);
    }
  }

  // Handle form submission result
  $effect(() => {
    if (form?.success && form?.submitted) {
      submissionFailed = false;
      submissionSuccess = true;

      // If analysis failed show manual entry
      if (form?.submissionId && form?.analysisComplete === false) {
        currentSubmissionId = form.submissionId as string;
        showManualEntryModal = true;
      }
    }
  });

</script>

<div>
  <PageHeader
    title={data.project.name}
    subtitle={data.project.description || 'Project details'}
  />

  <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
    <div class="flex flex-wrap gap-x-8 gap-y-3">
      <div class="flex-1 min-w-[200px]">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created By</h3>
        <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{data.lecturer ? data.lecturer.username : 'Unknown'}</p>
      </div>

      <div class="flex-1 min-w-[200px]">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created On</h3>
        <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{new Date(data.project.createdAt).toLocaleDateString()}</p>
      </div>

      {#if data.project.deadline}
        <div class="flex-1 min-w-[200px]">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Submission Deadline</h3>
          <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{formattedDeadline}</p>
          {#if !deadlinePassed}
            <div class="text-sm font-medium text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
              </svg>
              <Countdown deadline={data.project.deadline} warningThreshold={60} />
            </div>
          {:else}
            <div class="text-sm font-medium text-red-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              Deadline has passed
            </div>
          {/if}
        </div>
      {/if}

      {#if data.project.maxAttempts > 0}
        <div class="flex-1 min-w-[200px]">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Maximum Attempts</h3>
          <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{attemptCount} of {data.project.maxAttempts} used</p>
          {#if maxAttemptsReached}
            <div class="text-sm font-medium text-red-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              Maximum attempts reached
            </div>
          {/if}
        </div>
      {/if}
    </div>
  </div>

  <div class="flex flex-col md:flex-row gap-6">
    <div class="flex-1 md:flex-grow-[3]">

      {#if submissionSuccess}
        <Card>
          <div class="bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800 rounded-lg p-5 relative overflow-hidden">
            <div class="absolute top-0 right-0 -mt-4 -mr-4 h-16 w-16">
            </div>

            <div class="flex">
              <div class="flex-shrink-0">
              </div>
              <div class="ml-4 flex-1">
                <h3 class="text-base font-medium text-green-800">Submission Successful</h3>
                <div class="mt-2 text-sm text-green-700">
                  <p>{form?.message || 'Your submission has been received successfully. You can view your submission status in the submissions page.'}</p>
                </div>
                <div class="mt-4 flex space-x-3">
                  <Button
                    onClick={viewSubmissions}
                    variant="success"
                    size="sm"
                  >
                    View Submissions
                  </Button>
                  {#if canSubmit}
                    <Button
                      variant="secondary"
                      onClick={() => {
                        // Reset all form states
                        submissionSuccess = false;
                        submissionFailed = false;
                        selectedFile = null;
                        failureMessage = '';
                        form = null;

                        setTimeout(() => {
                          window.location.reload();
                        }, 100);
                      }}
                    >
                      Submit Another File
                    </Button>
                  {/if}
                </div>
              </div>
            </div>
          </div>
        </Card>
      {:else if submissionFailed}
        <Card>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-lg p-5 relative overflow-hidden">
            <div class="absolute top-0 right-0 -mt-4 -mr-4 h-16 w-16">
            </div>

            <div class="flex">
              <div class="ml-4 flex-1">
                <h3 class="text-base font-medium text-red-800">Submission Failed</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>{failureMessage || 'Your submission could not be processed. Please try again.'}</p>
                </div>
                <div class="mt-4 flex space-x-3">
                  {#if canSubmit}
                    <Button
                      variant="primary"
                      onClick={() => {
                        submissionFailed = false;
                        submissionSuccess = false;
                        selectedFile = null;
                        failureMessage = '';
                        form = null;

                        // Force a refresh of the component
                        setTimeout(() => {
                          window.location.reload();
                        }, 100);
                      }}
                    >
                      Try Again
                    </Button>
                  {/if}
                </div>
              </div>
            </div>
          </div>
        </Card>
      {:else if deadlinePassed}
        <Card>
          <div class="px-6 py-5">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Submission Closed</h2>
            <AlertMessage
              type="error"
              message="Submission Deadline Passed: The deadline for this project was {formattedDeadline}. You can no longer submit files."
            />
          </div>
        </Card>
      {:else if maxAttemptsReached}
        <Card>
          <div class="px-6 py-5">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Submission Closed</h2>
            <AlertMessage
              type="error"
              message="Maximum Attempts Reached: You have used all {data.project.maxAttempts} allowed submission attempts for this project."
            />
          </div>
        </Card>
      {:else}
        <Card>
          <div class="px-6 py-5">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Submit Your Work</h2>

            {#if data.project.maxAttempts > 0 && attemptCount > 0}
              <AlertMessage
                type="warning"
                message="Submission Attempts: You have used {attemptCount} of {data.project.maxAttempts} allowed submission attempts. Note: Each new submission will replace your previous one but will count as a new attempt."
                class="mb-4"
              />
            {/if}

            {#if deadlineApproaching()}
              <div class="mb-4">
                <AlertMessage
                  type="warning"
                  message="Deadline Approaching: The submission deadline is approaching: {formattedDeadline}"
                />
                <div class="mt-2 text-center font-medium">
                  <Countdown deadline={data.project.deadline} warningThreshold={60} />
                </div>
              </div>
            {/if}

            <form
              method="POST"
              action="?/submitFile"
              enctype="multipart/form-data"
              use:enhance={() => {
                isSubmitting = true;
                return ({ update }) => {
                  isSubmitting = false;
                  update();
                };
              }}
              class="space-y-4"
            >
              <input type="hidden" name="projectId" value={data.project.id} />

              <div>
                <label for="file-upload" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Upload File</label>
                {#if submissionCount > 0}
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    <svg class="inline-block h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                    Your new submission will replace your previous one.
                  </p>
                {/if}
                <div
                  role="button"
                  tabindex="0"
                  aria-label="Drop zone for file upload"
                  class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 {dragActive ? 'border-indigo-300 bg-indigo-50 dark:border-indigo-700 dark:bg-indigo-900/30' : 'border-gray-300 dark:border-gray-600'} border-dashed rounded-md transition-colors duration-200"
                  ondragenter={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    dragActive = true;
                  }}
                  ondragover={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    dragActive = true;
                  }}
                  ondragleave={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    dragActive = false;
                  }}
                  ondrop={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    dragActive = false;

                    if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
                      selectedFile = e.dataTransfer.files[0];
                      if (fileInputRef) {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(selectedFile);
                        fileInputRef.files = dataTransfer.files;
                      }
                    }
                  }}
                  onclick={() => {
                    if (fileInputRef) {
                      fileInputRef.click();
                    }
                  }}
                  onkeydown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      if (fileInputRef) {
                        fileInputRef.click();
                      }
                    }
                  }}
                >
                  <div class="space-y-1 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="flex text-sm text-gray-600 dark:text-gray-400">
                      <label for="file-upload" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 focus-within:outline-none">
                        <span>Upload a file</span>
                        <input
                          id="file-upload"
                          name="file"
                          type="file"
                          accept=".pdf,.zip,.rar,.7z,application/pdf,application/zip,application/x-zip-compressed,application/x-rar-compressed,application/x-7z-compressed,application/vnd.rar"
                          class="sr-only"
                          bind:this={fileInputRef}
                          onchange={(e) => handleFileChange(e)}
                          required
                        />
                      </label>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      PDF or compressed archive (.zip recommended) up to 20MB
                    </p>
                    {#if dragActive}
                      <p class="text-sm text-indigo-600 dark:text-indigo-400 font-medium">
                        Drop file here
                      </p>
                    {/if}
                  </div>
                </div>
                {#if selectedFile}
                  <div class="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <div class="flex items-center">
                      <svg class="h-5 w-5 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                      </svg>
                      <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{selectedFile.name}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                          {Math.round(selectedFile.size / 1024)} KB • {selectedFile.type || 'Unknown type'}
                        </p>
                      </div>
                    </div>
                    {#if selectedFile.name.toLowerCase().endsWith('.zip') || selectedFile.name.toLowerCase().endsWith('.rar') || selectedFile.name.toLowerCase().endsWith('.7z')}
                      <p class="mt-2 text-xs text-indigo-600 dark:text-indigo-400">
                        <svg class="inline-block h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                        The system will extract PDF and CS files from this archive
                      </p>
                    {/if}
                  </div>
                {/if}
              </div>

              {#if form?.message && !form?.success}
                <div class="rounded-md bg-red-50 dark:bg-red-900/30 p-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                      <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                        <p>{form.message}</p>
                      </div>
                    </div>
                  </div>
                </div>
              {/if}

              <div class="pt-4">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={isSubmitting || !selectedFile}
                >
                  {#if isSubmitting}
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting...
                  {:else}
                    Submit Document
                  {/if}
                </Button>
              </div>
            </form>
          </div>
        </Card>
      {/if}
    </div>

    <div class="md:w-1/4">
      <Card>
        <div class="px-6 py-5">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Your Submissions</h2>

          {#if data.submissions && data.submissions.length > 0}
            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
              {#each data.submissions as submission}
                <li class="py-3">
                  <div class="flex justify-between">
                    <div>
                      <p class="text-sm font-medium text-gray-900 dark:text-white">{submission.originalFilename}</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        Submitted on {new Date(submission.submittedAt).toLocaleString()}
                      </p>
                    </div>
                    <div class="flex items-center gap-2">
                      <Button
                      variant="light"
                      size="sm"
                        onClick={() => viewPdf(submission)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </Button>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {Math.round(submission.fileSize / 1024)} KB
                      </span>
                    </div>
                  </div>
                </li>
              {/each}
            </ul>
            <div class="mt-4 text-center">
              <a href="/dashboard/student/submissions" class="text-sm text-blue-600 hover:text-blue-800">
                View all submissions
              </a>
            </div>
          {:else}
            <p class="text-sm text-gray-500 dark:text-gray-400 italic">
              You haven't submitted any files for this project yet.
            </p>
          {/if}
        </div>
      </Card>
    </div>
  </div>

  <PDFViewerModal
    show={showPdfViewer}
    url={currentPdfUrl}
    title={currentPdfTitle}
    submissionId={currentSubmissionId}
    isAnalyzing={isAnalyzing}
    complexityData={complexityData}
    userRole="student"
    onclose={closePdfViewer}
    onanalyze={analyzeSubmission}
    onmanualsubmit={handleManualSubmit}
  />

  <ManualTableEntryModal
    show={showManualEntryModal}
    submissionId={currentSubmissionId}
    onclose={(data) => {
      showManualEntryModal = false;
      if (data?.cancelled && currentSubmissionId) {
        // Delete if the user cancelled
        deleteSubmission(currentSubmissionId);
      }
    }}
    onsubmit={handleManualSubmit}
  />
</div>
