import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type ThemeMode = 'light' | 'dark';

function getInitialTheme(): ThemeMode {
  if (!browser) return 'light';

  const savedTheme = localStorage.getItem('theme') as ThemeMode;
  if (savedTheme === 'light' || savedTheme === 'dark') return savedTheme;

  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

export const theme = writable<ThemeMode>(getInitialTheme());
if (browser) {
  theme.subscribe(value => {
    localStorage.setItem('theme', value);
    document.documentElement.classList.toggle('dark', value === 'dark');
  });

  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
    if (!localStorage.getItem('theme')) {
      theme.set(event.matches ? 'dark' : 'light');
    }
  });
}
export function toggleTheme(): void {
  theme.update(current => current === 'light' ? 'dark' : 'light');
}