import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Locale = 'en' | 'lt';

function getInitialLocale(): Locale {
  if (!browser) return 'en';

  const savedLocale = localStorage.getItem('locale') as Locale;
  if (savedLocale === 'en' || savedLocale === 'lt') return savedLocale;

  return navigator.language.startsWith('lt') ? 'lt' : 'en';
}

export const locale = writable<Locale>(getInitialLocale());
if (browser) {
  locale.subscribe(value => {
    localStorage.setItem('locale', value);
    document.documentElement.lang = value;
    document.cookie = `locale=${value};path=/;max-age=31536000`;
  });
}

export type TranslationKey =
  // Header navigation
  'header.dashboard' |
  'header.users' |
  'header.projects' |
  'header.approvals' |
  'header.submissions' |
  'header.system' |
  'header.adminManagement' |
  'header.groups' |
  'header.submit' |
  'header.organizations' |

  // Common UI elements
  'common.back' |
  'common.save' |
  'common.cancel' |
  'common.delete' |
  'common.create' |
  'common.edit' |
  'common.search' |
  'common.loading' |
  'common.noResults' |
  'common.error' |
  'common.success' |
  'common.user' |
  'common.settings' |
  'common.signOut' |
  'common.welcome' |
  'common.viewAll' |
  'common.viewDetails' |
  'common.actions' |
  'common.created' |
  'common.lastUpdated' |
  'common.deadline' |
  'common.noDeadline' |
  'common.deadlinePassed' |
  'common.expired' |
  'common.organization' |
  'common.yes' |
  'common.no' |
  'common.active' |
  'common.inactive' |
  'common.status' |
  'common.email' |
  'common.username' |
  'common.password' |
  'common.role' |
  'common.student' |
  'common.lecturer' |
  'common.admin' |
  'common.developer' |
  'common.allOrganizations' |
  'common.noMatch' |
  'common.unknown' |
  'common.project' |
  'common.submissions' |
  'common.location' |

  // Authentication
  'auth.login' |
  'auth.logout' |
  'auth.register' |
  'auth.username' |
  'auth.password' |
  'auth.email' |
  'auth.loginToAccount' |
  'auth.createAccount' |
  'auth.existingAccount' |
  'auth.accountType' |
  'auth.passwordRequirements' |
  'auth.approvalRequired' |
  'auth.forgotPassword' |
  'auth.resetPassword' |
  'auth.forgotPasswordTitle' |
  'auth.resetPasswordTitle' |
  'auth.forgotPasswordDesc' |
  'auth.resetPasswordDesc' |
  'auth.sendResetLink' |
  'auth.resetPasswordButton' |
  'auth.backToLogin' |
  'auth.passwordResetSuccess' |

  // Language settings
  'language.english' |
  'language.lithuanian' |
  'language.select' |

  // Theme settings
  'theme.switchToDark' |
  'theme.switchToLight' |
  'theme.dark' |
  'theme.light' |
  'theme.system' |
  'theme.select' |

  // Pending approval page
  'pending.title' |
  'pending.message' |
  'pending.contact' |

  // Home page
  'home.securePdf' |
  'home.forEducation' |
  'home.description' |
  'home.getStarted' |
  'home.learnMore' |
  'home.readyToStart' |
  'home.signUpToday' |

  // Dashboard common
  'dashboard.student.message' |
  'dashboard.lecturer.message' |
  'dashboard.admin.message' |
  'dashboard.developer.message' |
  'dashboard.default.message' |
  'dashboard.quickActions' |
  'dashboard.manageGroups' |
  'dashboard.manageGroupsDesc' |
  'dashboard.submissionManagement' |
  'dashboard.submissionManagementDesc' |
  'dashboard.viewSubmissions' |
  'dashboard.projectCreation' |
  'dashboard.projectCreationDesc' |
  'dashboard.createProject' |
  'dashboard.viewAllActions' |
  'dashboard.stats.submissions' |
  'dashboard.stats.projects' |
  'dashboard.stats.users' |
  'dashboard.stats.groups' |
  'dashboard.stats.activeAdmins' |
  'dashboard.stats.totalUsers' |
  'dashboard.stats.activeUsers' |
  'dashboard.stats.totalProjects' |
  'dashboard.stats.pendingApprovals' |
  'dashboard.stats.inactiveAdmins' |
  'dashboard.stats.allRolesCombined' |
  'dashboard.stats.totalAdmins' |
  'dashboard.stats.systemStats' |
  'dashboard.stats.totalUsersOrg' |
  'dashboard.stats.activeUsersOrg' |

  // Student dashboard
  'dashboard.student.title' |
  'dashboard.student.myProjects' |
  'dashboard.student.mySubmissions' |
  'dashboard.student.myGroups' |
  'dashboard.student.viewAssignedProjects' |
  'dashboard.student.viewSubmissionHistory' |
  'dashboard.student.viewLectureGroups' |
  'dashboard.student.recentActivity' |
  'dashboard.student.noRecentSubmissions' |
  'dashboard.student.noRecentSubmissionsDesc' |
  'dashboard.student.project' |
  'dashboard.student.notAssignedProjects' |
  'dashboard.student.viewAllProjects' |

  // Lecturer dashboard
  'dashboard.lecturer.title' |
  'dashboard.lecturer.createNewGroup' |
  'dashboard.lecturer.createNewGroupDesc' |
  'dashboard.lecturer.manageGroups' |
  'dashboard.lecturer.manageGroupsDesc' |
  'dashboard.lecturer.createNewProject' |
  'dashboard.lecturer.createNewProjectDesc' |
  'dashboard.lecturer.manageProjects' |
  'dashboard.lecturer.manageProjectsDesc' |
  'dashboard.lecturer.recentSubmissions' |
  'dashboard.lecturer.recentGroups' |
  'dashboard.lecturer.noDescription' |
  'dashboard.lecturer.organization' |

  // Lecturer groups page
  'dashboard.lecturer.groups.title' |
  'dashboard.lecturer.groups.createNewGroup' |
  'dashboard.lecturer.groups.noGroups' |
  'dashboard.lecturer.groups.noGroupsDesc' |
  'dashboard.lecturer.groups.lectureSchedule' |

  // Lecturer groups new page
  'dashboard.lecturer.groups.new.title' |
  'dashboard.lecturer.groups.new.groupDetails' |
  'dashboard.lecturer.groups.new.groupName' |
  'dashboard.lecturer.groups.new.groupDescription' |
  'dashboard.lecturer.groups.new.lectureSchedule' |
  'dashboard.lecturer.groups.new.noLectureTimes' |
  'dashboard.lecturer.groups.new.addTimeSlot' |
  'dashboard.lecturer.groups.new.day' |
  'dashboard.lecturer.groups.new.startTime' |
  'dashboard.lecturer.groups.new.endTime' |
  'dashboard.lecturer.groups.new.location' |
  'dashboard.lecturer.groups.new.actions' |
  'dashboard.lecturer.groups.new.remove' |
  'dashboard.lecturer.groups.new.createGroup' |

  // Admin dashboard
  'dashboard.admin.title' |
  'dashboard.admin.pendingApprovals' |
  'dashboard.admin.activeUsers' |
  'dashboard.admin.manageUsers' |
  'dashboard.admin.quickActions' |
  'dashboard.admin.manageOrgUsers' |
  'dashboard.admin.manageOrgUsersDesc' |
  'dashboard.admin.systemStats' |
  'dashboard.admin.totalUsers' |
  'dashboard.admin.totalProjects' |
  'dashboard.admin.viewAll' |
  'dashboard.admin.noPendingApprovals' |
  'dashboard.admin.noRecentApprovals' |

  // Admin users page
  'dashboard.admin.users.title' |
  'dashboard.admin.users.user' |
  'dashboard.admin.users.organization' |
  'dashboard.admin.users.role' |
  'dashboard.admin.users.status' |
  'dashboard.admin.users.created' |
  'dashboard.admin.users.actions' |
  'dashboard.admin.users.edit' |
  'dashboard.admin.users.activate' |
  'dashboard.admin.users.deactivate' |
  'dashboard.admin.users.noUsers' |

  // Admin approvals page
  'dashboard.admin.approvals.title' |
  'dashboard.admin.approvals.pendingApprovals' |
  'dashboard.admin.approvals.recentApprovals' |
  'dashboard.admin.approvals.username' |
  'dashboard.admin.approvals.email' |
  'dashboard.admin.approvals.registrationDate' |
  'dashboard.admin.approvals.actions' |
  'dashboard.admin.approvals.approve' |
  'dashboard.admin.approvals.reject' |

  // Developer dashboard
  'dashboard.developer.title' |
  'dashboard.developer.activeAdmins' |
  'dashboard.developer.manageAdmins' |
  'dashboard.developer.totalUsers' |
  'dashboard.developer.quickActions' |
  'dashboard.developer.createNewOrg' |
  'dashboard.developer.createNewOrgDesc' |
  'dashboard.developer.createNewAdmin' |
  'dashboard.developer.createNewAdminDesc' |
  'dashboard.developer.systemStats' |
  'dashboard.developer.totalStudents' |
  'dashboard.developer.totalLecturers' |
  'dashboard.developer.totalAdmins' |
  'dashboard.developer.totalProjects' |
  'dashboard.developer.totalSubmissions' |
  'dashboard.developer.allRolesCombined' |

  // Developer admins page
  'dashboard.developer.admins.title' |
  'dashboard.developer.admins.totalAdmins' |
  'dashboard.developer.admins.activeAdmins' |
  'dashboard.developer.admins.inactiveAdmins' |
  'dashboard.developer.admins.adminAccounts' |
  'dashboard.developer.admins.username' |
  'dashboard.developer.admins.email' |
  'dashboard.developer.admins.organization' |
  'dashboard.developer.admins.status' |
  'dashboard.developer.admins.created' |
  'dashboard.developer.admins.actions' |
  'dashboard.developer.admins.edit' |
  'dashboard.developer.admins.activate' |
  'dashboard.developer.admins.deactivate' |
  'dashboard.developer.admins.noAdmins' |
  'dashboard.developer.admins.noAdminsFilter' |

  // Developer new admin page
  'dashboard.developer.admins.new.title' |
  'dashboard.developer.admins.new.createAdmin' |
  'dashboard.developer.admins.new.adminDetails' |
  'dashboard.developer.admins.new.username' |
  'dashboard.developer.admins.new.usernameRequirements' |
  'dashboard.developer.admins.new.email' |
  'dashboard.developer.admins.new.password' |
  'dashboard.developer.admins.new.passwordRequirements' |
  'dashboard.developer.admins.new.organization' |
  'dashboard.developer.admins.new.selectOrganization' |
  'dashboard.developer.admins.new.noOrganization' |

  // Student submit page
  'dashboard.student.submit.title' |
  'dashboard.student.submit.description' |
  'dashboard.student.submit.availableProjects' |
  'dashboard.student.submit.noProjectsAvailable' |
  'dashboard.student.submit.noProjectsDesc' |
  'dashboard.student.submit.projectName' |
  'dashboard.student.submit.projectDescription' |
  'dashboard.student.submit.deadline' |
  'dashboard.student.submit.actions' |
  'dashboard.student.submit.noDescription' |
  'dashboard.student.submit.viewAndSubmit' |

  // Lecturer projects page
  'dashboard.lecturer.projects.title' |
  'dashboard.lecturer.projects.createNewProject' |
  'dashboard.lecturer.projects.noProjects' |
  'dashboard.lecturer.projects.noProjectsDesc' |
  'dashboard.lecturer.projects.getStarted' |

  // Debug page
  'debug.title' |
  'debug.headerStatus' |
  'debug.headerVisible' |
  'debug.pageData' |
  'debug.userData' |
  'debug.goToDashboard' |

  // App name
  'app.name';

const translations: Record<Locale, Record<TranslationKey, string>> = {
  en: {
    // Header navigation
    'header.dashboard': 'Dashboard',
    'header.users': 'Users',
    'header.projects': 'Projects',
    'header.approvals': 'Approvals',
    'header.submissions': 'Submissions',
    'header.system': 'System',
    'header.adminManagement': 'Admin Management',
    'header.groups': 'Groups',
    'header.submit': 'Submit',
    'header.organizations': 'Organizations',

    // Common UI elements
    'common.back': 'Back',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.create': 'Create',
    'common.edit': 'Settings',
    'common.search': 'Search',
    'common.loading': 'Loading...',
    'common.noResults': 'No results found',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.user': 'User',
    'common.settings': 'Settings',
    'common.signOut': 'Sign out',
    'common.welcome': 'Welcome',
    'common.viewAll': 'View All',
    'common.viewDetails': 'View Details',
    'common.actions': 'Actions',
    'common.created': 'Created',
    'common.lastUpdated': 'Last Updated',
    'common.deadline': 'Deadline',
    'common.noDeadline': 'No deadline',
    'common.deadlinePassed': 'Passed',
    'common.expired': 'Expired',
    'common.organization': 'Organization',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.active': 'Active',
    'common.inactive': 'Inactive',
    'common.status': 'Status',
    'common.email': 'Email',
    'common.username': 'Username',
    'common.password': 'Password',
    'common.role': 'Role',
    'common.student': 'Student',
    'common.lecturer': 'Lecturer',
    'common.admin': 'Admin',
    'common.developer': 'Developer',
    'common.allOrganizations': 'All Organizations',
    'common.noMatch': 'No results match the current filters',
    'common.unknown': 'Unknown',
    'common.project': 'Project',
    'common.submissions': 'Submissions',
    'common.location': 'Location',

    // Authentication
    'auth.login': 'Login',
    'auth.logout': 'Logout',
    'auth.register': 'Register',
    'auth.username': 'Username',
    'auth.password': 'Password',
    'auth.email': 'Email',
    'auth.loginToAccount': 'Sign in to your account',
    'auth.createAccount': 'Create your account',
    'auth.existingAccount': 'sign in to existing account',
    'auth.accountType': 'Account Type',
    'auth.passwordRequirements': 'Password requirements',
    'auth.approvalRequired': 'Note: Lecturer accounts require approval by an administrator before they become active.',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.resetPassword': 'Reset Password',
    'auth.forgotPasswordTitle': 'Forgot Password',
    'auth.resetPasswordTitle': 'Reset Password',
    'auth.forgotPasswordDesc': 'Enter your email address and we\'ll send you a link to reset your password.',
    'auth.resetPasswordDesc': 'Enter your new password below.',
    'auth.sendResetLink': 'Send Reset Link',
    'auth.resetPasswordButton': 'Reset Password',
    'auth.backToLogin': 'Back to Login',
    'auth.passwordResetSuccess': 'Your password has been reset successfully. You can now log in with your new password.',

    // Language settings
    'language.english': 'EN',
    'language.lithuanian': 'LT',
    'language.select': 'Select language',

    // Theme settings
    'theme.switchToDark': 'Switch to dark mode',
    'theme.switchToLight': 'Switch to light mode',
    'theme.dark': 'Dark',
    'theme.light': 'Light',
    'theme.system': 'System',
    'theme.select': 'Select theme',

    // Pending approval page
    'pending.title': 'Account Pending Approval',
    'pending.message': 'Your lecturer account is currently pending administrator approval. You\'ll receive an email notification once your account has been approved.',
    'pending.contact': 'If you think this is a mistake or have urgent questions, please contact the system administrator.',

    // Home page
    'home.securePdf': 'Report checking system',
    'home.forEducation': 'for educational institutions',
    'home.description': 'PDFBankas offers a solution for managing student submissions for educational institutions.',
    'home.getStarted': 'Get started',
    'home.learnMore': 'Learn more',
    'home.readyToStart': 'Ready to get started?',
    'home.signUpToday': 'Sign up for an account today.',

    // Dashboard common
    'dashboard.student.message': 'Manage your submissions and groups',
    'dashboard.lecturer.message': 'Manage your projects, groups, and submissions',
    'dashboard.admin.message': 'Administer users, projects, and approvals',
    'dashboard.developer.message': 'System administration and monitoring',
    'dashboard.default.message': 'Access your dashboard',
    'dashboard.quickActions': 'Quick Actions',
    'dashboard.manageGroups': 'Manage Groups',
    'dashboard.manageGroupsDesc': 'Create and manage student groups for your projects',
    'dashboard.submissionManagement': 'Submission Management',
    'dashboard.submissionManagementDesc': 'View and manage all document submissions and their status',
    'dashboard.viewSubmissions': 'View Submissions',
    'dashboard.projectCreation': 'Project Creation',
    'dashboard.projectCreationDesc': 'Create a new project and assign students or groups',
    'dashboard.createProject': 'Create Project',
    'dashboard.viewAllActions': 'View All Actions',

    // Dashboard stats
    'dashboard.stats.submissions': 'Submissions',
    'dashboard.stats.projects': 'Projects',
    'dashboard.stats.users': 'Users',
    'dashboard.stats.groups': 'Groups',
    'dashboard.stats.activeAdmins': 'Active Admins',
    'dashboard.stats.totalUsers': 'Total Users',
    'dashboard.stats.activeUsers': 'Active Users',
    'dashboard.stats.totalProjects': 'Total Projects',
    'dashboard.stats.pendingApprovals': 'Pending Approvals',
    'dashboard.stats.inactiveAdmins': 'Inactive Admins',
    'dashboard.stats.allRolesCombined': 'All roles combined',
    'dashboard.stats.totalAdmins': 'Total Admins',
    'dashboard.stats.systemStats': 'System Stats',
    'dashboard.stats.totalUsersOrg': 'Total Users (Organization)',
    'dashboard.stats.activeUsersOrg': 'Active Users (Organization)',

    // Student dashboard
    'dashboard.student.title': 'Student Dashboard',
    'dashboard.student.myProjects': 'My Projects',
    'dashboard.student.mySubmissions': 'My Submissions',
    'dashboard.student.myGroups': 'My Groups',
    'dashboard.student.viewAssignedProjects': 'View your assigned projects',
    'dashboard.student.viewSubmissionHistory': 'View your submission history',
    'dashboard.student.viewLectureGroups': 'View your lecture groups and schedules',
    'dashboard.student.recentActivity': 'Recent Activity',
    'dashboard.student.noRecentSubmissions': 'No recent submissions',
    'dashboard.student.noRecentSubmissionsDesc': 'Go to the Submit page to upload your work.',
    'dashboard.student.project': 'Project',
    'dashboard.student.notAssignedProjects': 'You are not assigned to any projects yet.',
    'dashboard.student.viewAllProjects': 'View all projects',

    // Lecturer dashboard
    'dashboard.lecturer.title': 'Lecturer Dashboard',
    'dashboard.lecturer.createNewGroup': 'Create New Group',
    'dashboard.lecturer.createNewGroupDesc': 'Create a new student group with lecture schedule',
    'dashboard.lecturer.manageGroups': 'Manage Groups',
    'dashboard.lecturer.manageGroupsDesc': 'View and manage your student groups',
    'dashboard.lecturer.createNewProject': 'Create New Project',
    'dashboard.lecturer.createNewProjectDesc': 'Set up a new testing project',
    'dashboard.lecturer.manageProjects': 'Manage Projects',
    'dashboard.lecturer.manageProjectsDesc': 'View and edit your existing projects',
    'dashboard.lecturer.recentSubmissions': 'Recent Submissions',
    'dashboard.lecturer.recentGroups': 'Recent Groups',
    'dashboard.lecturer.noDescription': 'No description',
    'dashboard.lecturer.organization': 'Organization',

    // Lecturer groups page
    'dashboard.lecturer.groups.title': 'Student Groups',
    'dashboard.lecturer.groups.createNewGroup': 'Create New Group',
    'dashboard.lecturer.groups.noGroups': 'No groups found',
    'dashboard.lecturer.groups.noGroupsDesc': 'Get started by creating a new group.',
    'dashboard.lecturer.groups.lectureSchedule': 'Lecture Schedule',

    // Lecturer groups new page
    'dashboard.lecturer.groups.new.title': 'Create New Group',
    'dashboard.lecturer.groups.new.groupDetails': 'Group Details',
    'dashboard.lecturer.groups.new.groupName': 'Group Name',
    'dashboard.lecturer.groups.new.groupDescription': 'Group Description (optional)',
    'dashboard.lecturer.groups.new.lectureSchedule': 'Lecture Schedule',
    'dashboard.lecturer.groups.new.noLectureTimes': 'No lecture times added. Click "Add Time Slot" to schedule lectures.',
    'dashboard.lecturer.groups.new.addTimeSlot': 'Add Time Slot',
    'dashboard.lecturer.groups.new.day': 'Day',
    'dashboard.lecturer.groups.new.startTime': 'Start Time',
    'dashboard.lecturer.groups.new.endTime': 'End Time',
    'dashboard.lecturer.groups.new.location': 'Location',
    'dashboard.lecturer.groups.new.actions': 'Actions',
    'dashboard.lecturer.groups.new.remove': 'Remove',
    'dashboard.lecturer.groups.new.createGroup': 'Create Group',

    // Admin dashboard
    'dashboard.admin.title': 'Admin Dashboard',
    'dashboard.admin.pendingApprovals': 'Pending Approvals',
    'dashboard.admin.activeUsers': 'Active Users',
    'dashboard.admin.manageUsers': 'Manage users',
    'dashboard.admin.quickActions': 'Quick Actions',
    'dashboard.admin.manageOrgUsers': 'Manage Organization Users',
    'dashboard.admin.manageOrgUsersDesc': 'Manage users in your organization',
    'dashboard.admin.systemStats': 'System Stats',
    'dashboard.admin.totalUsers': 'Total Users',
    'dashboard.admin.totalProjects': 'Total Projects',
    'dashboard.admin.viewAll': 'View all',
    'dashboard.admin.noPendingApprovals': 'No pending approval requests at this time.',
    'dashboard.admin.noRecentApprovals': 'No recently approved lecturers.',

    // Admin users page
    'dashboard.admin.users.title': 'User Management',
    'dashboard.admin.users.user': 'User',
    'dashboard.admin.users.organization': 'Organization',
    'dashboard.admin.users.role': 'Role',
    'dashboard.admin.users.status': 'Status',
    'dashboard.admin.users.created': 'Created',
    'dashboard.admin.users.actions': 'Actions',
    'dashboard.admin.users.edit': 'Edit',
    'dashboard.admin.users.activate': 'Activate',
    'dashboard.admin.users.deactivate': 'Deactivate',
    'dashboard.admin.users.noUsers': 'No users matching the current filters',

    // Admin approvals page
    'dashboard.admin.approvals.title': 'Approval Requests',
    'dashboard.admin.approvals.pendingApprovals': 'Pending Approvals',
    'dashboard.admin.approvals.recentApprovals': 'Recently Approved',
    'dashboard.admin.approvals.username': 'Username',
    'dashboard.admin.approvals.email': 'Email',
    'dashboard.admin.approvals.registrationDate': 'Registration Date',
    'dashboard.admin.approvals.actions': 'Actions',
    'dashboard.admin.approvals.approve': 'Approve',
    'dashboard.admin.approvals.reject': 'Reject',

    // Developer dashboard
    'dashboard.developer.title': 'Developer Dashboard',
    'dashboard.developer.activeAdmins': 'Active Admins',
    'dashboard.developer.manageAdmins': 'Manage admins',
    'dashboard.developer.totalUsers': 'Total Users',
    'dashboard.developer.quickActions': 'Quick Actions',
    'dashboard.developer.createNewOrg': 'Create New Organization',
    'dashboard.developer.createNewOrgDesc': 'Add a new organization to the system',
    'dashboard.developer.createNewAdmin': 'Create New Admin',
    'dashboard.developer.createNewAdminDesc': 'Add a new administrator to the system',
    'dashboard.developer.systemStats': 'System Stats',
    'dashboard.developer.totalStudents': 'Total Students',
    'dashboard.developer.totalLecturers': 'Total Lecturers',
    'dashboard.developer.totalAdmins': 'Total Admins',
    'dashboard.developer.totalProjects': 'Total Projects',
    'dashboard.developer.totalSubmissions': 'Total Submissions',
    'dashboard.developer.allRolesCombined': 'All roles combined',

    // Developer admins page
    'dashboard.developer.admins.title': 'Admin Management',
    'dashboard.developer.admins.totalAdmins': 'Total Admins',
    'dashboard.developer.admins.activeAdmins': 'Active Admins',
    'dashboard.developer.admins.inactiveAdmins': 'Inactive Admins',
    'dashboard.developer.admins.adminAccounts': 'Admin Accounts',
    'dashboard.developer.admins.username': 'Username',
    'dashboard.developer.admins.email': 'Email',
    'dashboard.developer.admins.organization': 'Organization',
    'dashboard.developer.admins.status': 'Status',
    'dashboard.developer.admins.created': 'Created',
    'dashboard.developer.admins.actions': 'Actions',
    'dashboard.developer.admins.edit': 'Edit',
    'dashboard.developer.admins.activate': 'Activate',
    'dashboard.developer.admins.deactivate': 'Deactivate',
    'dashboard.developer.admins.noAdmins': 'No admin accounts found',
    'dashboard.developer.admins.noAdminsFilter': 'No admins match the selected filter',

    // Developer new admin page
    'dashboard.developer.admins.new.title': 'Create New Admin',
    'dashboard.developer.admins.new.createAdmin': 'Create Admin',
    'dashboard.developer.admins.new.adminDetails': 'Admin Details',
    'dashboard.developer.admins.new.username': 'Username',
    'dashboard.developer.admins.new.usernameRequirements': '3-31 characters, lowercase letters, numbers, underscores, and hyphens only',
    'dashboard.developer.admins.new.email': 'Email',
    'dashboard.developer.admins.new.password': 'Password',
    'dashboard.developer.admins.new.passwordRequirements': 'At least 8 characters, including a number and a special character',
    'dashboard.developer.admins.new.organization': 'Organization',
    'dashboard.developer.admins.new.selectOrganization': 'Select an organization',
    'dashboard.developer.admins.new.noOrganization': 'No organization (system-wide admin)',

    // Student submit page
    'dashboard.student.submit.title': 'My Projects',
    'dashboard.student.submit.description': 'View and submit work to your assigned projects',
    'dashboard.student.submit.availableProjects': 'Available Projects',
    'dashboard.student.submit.noProjectsAvailable': 'No Projects Available',
    'dashboard.student.submit.noProjectsDesc': 'You are not assigned to any projects. Please contact your lecturer.',
    'dashboard.student.submit.projectName': 'Project Name',
    'dashboard.student.submit.projectDescription': 'Description',
    'dashboard.student.submit.deadline': 'Deadline',
    'dashboard.student.submit.actions': 'Actions',
    'dashboard.student.submit.noDescription': 'No description',
    'dashboard.student.submit.viewAndSubmit': 'View & Submit',

    // Lecturer projects page
    'dashboard.lecturer.projects.title': 'My Projects',
    'dashboard.lecturer.projects.createNewProject': 'Create New Project',
    'dashboard.lecturer.projects.noProjects': 'No projects',
    'dashboard.lecturer.projects.noProjectsDesc': 'Get started by creating a new project.',
    'dashboard.lecturer.projects.getStarted': 'Get started by creating a new project.',

    // Debug page
    'debug.title': 'Debug Page - Session Data',
    'debug.headerStatus': 'Header Status:',
    'debug.headerVisible': 'Is header element visible in DOM:',
    'debug.pageData': 'Page Data:',
    'debug.userData': 'User Data:',
    'debug.goToDashboard': 'Go to Dashboard',

    // App name
    'app.name': 'PDFBankas'
  },
  lt: {
    // Header navigation
    'header.dashboard': 'Valdymo skydelis',
    'header.users': 'Vartotojai',
    'header.projects': 'Projektai',
    'header.approvals': 'Patvirtinimai',
    'header.submissions': 'Pateiktys',
    'header.system': 'Sistema',
    'header.adminManagement': 'Administratorių valdymas',
    'header.groups': 'Grupės',
    'header.submit': 'Užduotys',
    'header.organizations': 'Organizacijos',

    // Common UI elements
    'common.back': 'Atgal',
    'common.save': 'Išsaugoti',
    'common.cancel': 'Atšaukti',
    'common.delete': 'Ištrinti',
    'common.create': 'Sukurti',
    'common.edit': 'Nustatymai',
    'common.search': 'Ieškoti',
    'common.loading': 'Kraunama...',
    'common.noResults': 'Rezultatų nerasta',
    'common.error': 'Klaida',
    'common.success': 'Sėkmingai',
    'common.user': 'Vartotojas',
    'common.settings': 'Nustatymai',
    'common.signOut': 'Atsijungti',
    'common.welcome': 'Sveiki',
    'common.viewAll': 'Rodyti viską',
    'common.viewDetails': 'Peržiūrėti detaliau',
    'common.actions': 'Veiksmai',
    'common.created': 'Sukurta',
    'common.lastUpdated': 'Paskutinį kartą atnaujinta',
    'common.deadline': 'Terminas',
    'common.noDeadline': 'Nėra termino',
    'common.deadlinePassed': 'Pasibaigęs',
    'common.expired': 'Baigėsi galiojimas',
    'common.organization': 'Organizacija',
    'common.yes': 'Taip',
    'common.no': 'Ne',
    'common.active': 'Aktyvus',
    'common.inactive': 'Neaktyvus',
    'common.status': 'Būsena',
    'common.email': 'El. paštas',
    'common.username': 'Vartotojo vardas',
    'common.password': 'Slaptažodis',
    'common.role': 'Rolė',
    'common.student': 'Studentas',
    'common.lecturer': 'Dėstytojas',
    'common.admin': 'Administratorius',
    'common.developer': 'Kūrėjas',
    'common.allOrganizations': 'Visos organizacijos',
    'common.noMatch': 'Nėra rezultatų, atitinkančių dabartinius filtrus',
    'common.unknown': 'Nežinoma',
    'common.project': 'Projektas',
    'common.submissions': 'Pateiktys',
    'common.location': 'Vieta',

    // Authentication
    'auth.login': 'Prisijungti',
    'auth.logout': 'Atsijungti',
    'auth.register': 'Registruotis',
    'auth.username': 'Vartotojo vardas',
    'auth.password': 'Slaptažodis',
    'auth.email': 'El. paštas',
    'auth.loginToAccount': 'Prisijunkite prie savo paskyros',
    'auth.createAccount': 'Sukurkite savo paskyrą',
    'auth.existingAccount': 'prisijunkite prie esamos paskyros',
    'auth.accountType': 'Paskyros tipas',
    'auth.passwordRequirements': 'Slaptažodžio reikalavimai',
    'auth.approvalRequired': 'Pastaba: Dėstytojų paskyras turi patvirtinti administratorius, kad jos būtų aktyvuotos.',
    'auth.forgotPassword': 'Pamiršote slaptažodį?',
    'auth.resetPassword': 'Atstatyti slaptažodį',
    'auth.forgotPasswordTitle': 'Pamiršote slaptažodį',
    'auth.resetPasswordTitle': 'Atstatyti slaptažodį',
    'auth.forgotPasswordDesc': 'Įveskite savo el. pašto adresą ir mes atsiųsime jums nuorodą slaptažodžio atstatymui.',
    'auth.resetPasswordDesc': 'Įveskite naują slaptažodį žemiau.',
    'auth.sendResetLink': 'Siųsti atstatymo nuorodą',
    'auth.resetPasswordButton': 'Atstatyti slaptažodį',
    'auth.backToLogin': 'Grįžti į prisijungimą',
    'auth.passwordResetSuccess': 'Jūsų slaptažodis sėkmingai atstatytas. Dabar galite prisijungti naudodami naują slaptažodį.',

    // Language settings
    'language.english': 'EN',
    'language.lithuanian': 'LT',
    'language.select': 'Pasirinkite kalbą',

    // Theme settings
    'theme.switchToDark': 'Perjungti į tamsų režimą',
    'theme.switchToLight': 'Perjungti į šviesų režimą',
    'theme.dark': 'Tamsus',
    'theme.light': 'Šviesus',
    'theme.system': 'Sistemos',
    'theme.select': 'Pasirinkite temą',

    // Pending approval page
    'pending.title': 'Paskyra laukia patvirtinimo',
    'pending.message': 'Jūsų dėstytojo paskyra šiuo metu laukia administratoriaus patvirtinimo. Kai jūsų paskyra bus patvirtinta, gausite pranešimą el. paštu.',
    'pending.contact': 'Jei manote, kad įvyko klaida, arba turite skubių klausimų, susisiekite su sistemos administratoriumi.',

    // Home page
    'home.securePdf': 'Ataskaitų tikrinimo sistema',
    'home.forEducation': 'švietimo įstaigoms',
    'home.description': 'PDFBankas siūlo darbų pateikimų valdymą švietimo įstaigoms.',
    'home.getStarted': 'Pradėti',
    'home.learnMore': 'Sužinoti daugiau',
    'home.readyToStart': 'Pasiruošę pradėti?',
    'home.signUpToday': 'Užsiregistruokite jau šiandien.',

    // Dashboard common
    'dashboard.student.message': 'Valdykite savo pateiktis ir grupes',
    'dashboard.lecturer.message': 'Valdykite savo projektus, grupes ir pateiktis',
    'dashboard.admin.message': 'Administruokite vartotojus, projektus ir patvirtinimus',
    'dashboard.developer.message': 'Sistemos administravimas ir stebėjimas',
    'dashboard.default.message': 'Pasiekite savo valdymo skydelį',
    'dashboard.quickActions': 'Greiti veiksmai',
    'dashboard.manageGroups': 'Valdyti grupes',
    'dashboard.manageGroupsDesc': 'Kurkite ir valdykite studentų grupes savo projektams',
    'dashboard.submissionManagement': 'Pateikčių valdymas',
    'dashboard.submissionManagementDesc': 'Peržiūrėkite ir valdykite visas dokumentų pateiktis ir jų būsenas',
    'dashboard.viewSubmissions': 'Peržiūrėti pateiktis',
    'dashboard.projectCreation': 'Projekto kūrimas',
    'dashboard.projectCreationDesc': 'Sukurkite naują projektą ir priskirkite studentus ar grupes',
    'dashboard.createProject': 'Sukurti projektą',
    'dashboard.viewAllActions': 'Rodyti visus veiksmus',

    // Dashboard stats
    'dashboard.stats.submissions': 'Pateiktys',
    'dashboard.stats.projects': 'Projektai',
    'dashboard.stats.users': 'Vartotojai',
    'dashboard.stats.groups': 'Grupės',
    'dashboard.stats.activeAdmins': 'Aktyvūs administratoriai',
    'dashboard.stats.totalUsers': 'Visi vartotojai',
    'dashboard.stats.activeUsers': 'Aktyvūs vartotojai',
    'dashboard.stats.totalProjects': 'Visi projektai',
    'dashboard.stats.pendingApprovals': 'Laukiantys patvirtinimo',
    'dashboard.stats.inactiveAdmins': 'Neaktyvūs administratoriai',
    'dashboard.stats.allRolesCombined': 'Visos rolės kartu',
    'dashboard.stats.totalAdmins': 'Visi administratoriai',
    'dashboard.stats.systemStats': 'Sistemos statistika',
    'dashboard.stats.totalUsersOrg': 'Visi vartotojai (Organizacija)',
    'dashboard.stats.activeUsersOrg': 'Aktyvūs vartotojai (Organizacija)',

    // Student dashboard
    'dashboard.student.title': 'Studento valdymo skydelis',
    'dashboard.student.myProjects': 'Mano projektai',
    'dashboard.student.mySubmissions': 'Mano pateiktys',
    'dashboard.student.myGroups': 'Mano grupės',
    'dashboard.student.viewAssignedProjects': 'Peržiūrėti priskirtus projektus',
    'dashboard.student.viewSubmissionHistory': 'Peržiūrėti pateikčių istoriją',
    'dashboard.student.viewLectureGroups': 'Peržiūrėti paskaitos grupes ir tvarkaraščius',
    'dashboard.student.recentActivity': 'Naujausia veikla',
    'dashboard.student.noRecentSubmissions': 'Nėra naujausių pateikčių',
    'dashboard.student.noRecentSubmissionsDesc': 'Eikite į pateikimo puslapį, kad įkeltumėte savo darbą.',
    'dashboard.student.project': 'Projektas',
    'dashboard.student.notAssignedProjects': 'Jums dar nepriskirti jokie projektai.',
    'dashboard.student.viewAllProjects': 'Peržiūrėti visus projektus',

    // Lecturer dashboard
    'dashboard.lecturer.title': 'Dėstytojo valdymo skydelis',
    'dashboard.lecturer.createNewGroup': 'Sukurti naują grupę',
    'dashboard.lecturer.createNewGroupDesc': 'Sukurti naują studentų grupę su paskaitos tvarkaraščiu',
    'dashboard.lecturer.manageGroups': 'Valdyti grupes',
    'dashboard.lecturer.manageGroupsDesc': 'Peržiūrėti ir valdyti savo studentų grupes',
    'dashboard.lecturer.createNewProject': 'Sukurti naują projektą',
    'dashboard.lecturer.createNewProjectDesc': 'Sukurti naują testavimo projektą',
    'dashboard.lecturer.manageProjects': 'Valdyti projektus',
    'dashboard.lecturer.manageProjectsDesc': 'Peržiūrėti ir redaguoti esamus projektus',
    'dashboard.lecturer.recentSubmissions': 'Naujausios pateiktys',
    'dashboard.lecturer.recentGroups': 'Naujausios grupės',
    'dashboard.lecturer.noDescription': 'Nėra aprašymo',
    'dashboard.lecturer.organization': 'Organizacija',

    // Lecturer groups page
    'dashboard.lecturer.groups.title': 'Studentų grupės',
    'dashboard.lecturer.groups.createNewGroup': 'Sukurti naują grupę',
    'dashboard.lecturer.groups.noGroups': 'Nėra grupių',
    'dashboard.lecturer.groups.noGroupsDesc': 'Pradėkite kurdami naują grupę.',
    'dashboard.lecturer.groups.lectureSchedule': 'Paskaitų tvarkaraštis',

    // Lecturer groups new page
    'dashboard.lecturer.groups.new.title': 'Sukurti naują grupę',
    'dashboard.lecturer.groups.new.groupDetails': 'Grupės informacija',
    'dashboard.lecturer.groups.new.groupName': 'Grupės pavadinimas',
    'dashboard.lecturer.groups.new.groupDescription': 'Grupės aprašymas (neprivaloma)',
    'dashboard.lecturer.groups.new.lectureSchedule': 'Paskaitų tvarkaraštis',
    'dashboard.lecturer.groups.new.noLectureTimes': 'Nėra pridėtų paskaitų laikų. Spauskite "Pridėti laiko tarpą" norėdami suplanuoti paskaitas.',
    'dashboard.lecturer.groups.new.addTimeSlot': 'Pridėti laiko tarpą',
    'dashboard.lecturer.groups.new.day': 'Diena',
    'dashboard.lecturer.groups.new.startTime': 'Pradžios laikas',
    'dashboard.lecturer.groups.new.endTime': 'Pabaigos laikas',
    'dashboard.lecturer.groups.new.location': 'Vieta',
    'dashboard.lecturer.groups.new.actions': 'Veiksmai',
    'dashboard.lecturer.groups.new.remove': 'Pašalinti',
    'dashboard.lecturer.groups.new.createGroup': 'Sukurti grupę',

    // Admin dashboard
    'dashboard.admin.title': 'Administratoriaus valdymo skydelis',
    'dashboard.admin.pendingApprovals': 'Laukiantys patvirtinimo',
    'dashboard.admin.activeUsers': 'Aktyvūs vartotojai',
    'dashboard.admin.manageUsers': 'Valdyti vartotojus',
    'dashboard.admin.quickActions': 'Greiti veiksmai',
    'dashboard.admin.manageOrgUsers': 'Valdyti organizacijos vartotojus',
    'dashboard.admin.manageOrgUsersDesc': 'Valdyti vartotojus jūsų organizacijoje',
    'dashboard.admin.systemStats': 'Sistemos statistika',
    'dashboard.admin.totalUsers': 'Visi vartotojai',
    'dashboard.admin.totalProjects': 'Visi projektai',
    'dashboard.admin.viewAll': 'Rodyti viską',
    'dashboard.admin.noPendingApprovals': 'Šiuo metu nėra laukiančių patvirtinimo prašymų.',
    'dashboard.admin.noRecentApprovals': 'Nėra neseniai patvirtintų dėstytojų.',

    // Admin users page
    'dashboard.admin.users.title': 'Vartotojų valdymas',
    'dashboard.admin.users.user': 'Vartotojas',
    'dashboard.admin.users.organization': 'Organizacija',
    'dashboard.admin.users.role': 'Rolė',
    'dashboard.admin.users.status': 'Būsena',
    'dashboard.admin.users.created': 'Sukurta',
    'dashboard.admin.users.actions': 'Veiksmai',
    'dashboard.admin.users.edit': 'Redaguoti',
    'dashboard.admin.users.activate': 'Aktyvuoti',
    'dashboard.admin.users.deactivate': 'Deaktyvuoti',
    'dashboard.admin.users.noUsers': 'Nėra vartotojų, atitinkančių dabartinius filtrus',

    // Admin approvals page
    'dashboard.admin.approvals.title': 'Patvirtinimo prašymai',
    'dashboard.admin.approvals.pendingApprovals': 'Laukiantys patvirtinimo',
    'dashboard.admin.approvals.recentApprovals': 'Neseniai patvirtinti',
    'dashboard.admin.approvals.username': 'Vartotojo vardas',
    'dashboard.admin.approvals.email': 'El. paštas',
    'dashboard.admin.approvals.registrationDate': 'Registracijos data',
    'dashboard.admin.approvals.actions': 'Veiksmai',
    'dashboard.admin.approvals.approve': 'Patvirtinti',
    'dashboard.admin.approvals.reject': 'Atmesti',

    // Developer dashboard
    'dashboard.developer.title': 'Kūrėjo valdymo skydelis',
    'dashboard.developer.activeAdmins': 'Aktyvūs administratoriai',
    'dashboard.developer.manageAdmins': 'Valdyti administratorius',
    'dashboard.developer.totalUsers': 'Visi vartotojai',
    'dashboard.developer.quickActions': 'Greiti veiksmai',
    'dashboard.developer.createNewOrg': 'Sukurti naują organizaciją',
    'dashboard.developer.createNewOrgDesc': 'Pridėti naują organizaciją į sistemą',
    'dashboard.developer.createNewAdmin': 'Sukurti naują administratorių',
    'dashboard.developer.createNewAdminDesc': 'Pridėti naują administratorių į sistemą',
    'dashboard.developer.systemStats': 'Sistemos statistika',
    'dashboard.developer.totalStudents': 'Visi studentai',
    'dashboard.developer.totalLecturers': 'Visi dėstytojai',
    'dashboard.developer.totalAdmins': 'Visi administratoriai',
    'dashboard.developer.totalProjects': 'Visi projektai',
    'dashboard.developer.totalSubmissions': 'Visos pateiktys',
    'dashboard.developer.allRolesCombined': 'Visos rolės kartu',

    // Developer admins page
    'dashboard.developer.admins.title': 'Administratorių valdymas',
    'dashboard.developer.admins.totalAdmins': 'Visi administratoriai',
    'dashboard.developer.admins.activeAdmins': 'Aktyvūs administratoriai',
    'dashboard.developer.admins.inactiveAdmins': 'Neaktyvūs administratoriai',
    'dashboard.developer.admins.adminAccounts': 'Administratorių paskyros',
    'dashboard.developer.admins.username': 'Vartotojo vardas',
    'dashboard.developer.admins.email': 'El. paštas',
    'dashboard.developer.admins.organization': 'Organizacija',
    'dashboard.developer.admins.status': 'Būsena',
    'dashboard.developer.admins.created': 'Sukurta',
    'dashboard.developer.admins.actions': 'Veiksmai',
    'dashboard.developer.admins.edit': 'Redaguoti',
    'dashboard.developer.admins.activate': 'Aktyvuoti',
    'dashboard.developer.admins.deactivate': 'Deaktyvuoti',
    'dashboard.developer.admins.noAdmins': 'Nerasta administratorių paskyrų',
    'dashboard.developer.admins.noAdminsFilter': 'Nėra administratorių, atitinkančių pasirinktą filtrą',

    // Developer new admin page
    'dashboard.developer.admins.new.title': 'Sukurti naują administratorių',
    'dashboard.developer.admins.new.createAdmin': 'Sukurti administratorių',
    'dashboard.developer.admins.new.adminDetails': 'Administratoriaus informacija',
    'dashboard.developer.admins.new.username': 'Vartotojo vardas',
    'dashboard.developer.admins.new.usernameRequirements': '3-31 simboliai, tik mažosios raidės, skaičiai, pabraukimai ir brūkšneliai',
    'dashboard.developer.admins.new.email': 'El. paštas',
    'dashboard.developer.admins.new.password': 'Slaptažodis',
    'dashboard.developer.admins.new.passwordRequirements': 'Mažiausiai 8 simboliai, įskaitant skaičių ir specialų simbolį',
    'dashboard.developer.admins.new.organization': 'Organizacija',
    'dashboard.developer.admins.new.selectOrganization': 'Pasirinkite organizaciją',
    'dashboard.developer.admins.new.noOrganization': 'Nėra organizacijos (sistemos administratorius)',

    // Student submit page
    'dashboard.student.submit.title': 'Mano projektai',
    'dashboard.student.submit.description': 'Peržiūrėti ir pateikti darbus priskirtiems projektams',
    'dashboard.student.submit.availableProjects': 'Galimi projektai',
    'dashboard.student.submit.noProjectsAvailable': 'Nėra galimų projektų',
    'dashboard.student.submit.noProjectsDesc': 'Jums nepriskirti jokie projektai. Susisiekite su savo dėstytoju.',
    'dashboard.student.submit.projectName': 'Projekto pavadinimas',
    'dashboard.student.submit.projectDescription': 'Aprašymas',
    'dashboard.student.submit.deadline': 'Terminas',
    'dashboard.student.submit.actions': 'Veiksmai',
    'dashboard.student.submit.noDescription': 'Nėra aprašymo',
    'dashboard.student.submit.viewAndSubmit': 'Peržiūrėti ir pateikti',

    // Lecturer projects page
    'dashboard.lecturer.projects.title': 'Mano projektai',
    'dashboard.lecturer.projects.createNewProject': 'Sukurti naują projektą',
    'dashboard.lecturer.projects.noProjects': 'Nėra projektų',
    'dashboard.lecturer.projects.noProjectsDesc': 'Pradėkite kurdami naują projektą.',
    'dashboard.lecturer.projects.getStarted': 'Pradėkite kurdami naują projektą.',

    // Debug page
    'debug.title': 'Derinimo puslapis - Sesijos duomenys',
    'debug.headerStatus': 'Antraštės būsena:',
    'debug.headerVisible': 'Ar antraštės elementas matomas DOM:',
    'debug.pageData': 'Puslapio duomenys:',
    'debug.userData': 'Vartotojo duomenys:',
    'debug.goToDashboard': 'Eiti į valdymo skydelį',

    // App name
    'app.name': 'PDFBankas'
  }
};

export function t(key: TranslationKey, currentLocale: Locale = 'en'): string {
  return translations[currentLocale][key] || translations['en'][key] || key;
}