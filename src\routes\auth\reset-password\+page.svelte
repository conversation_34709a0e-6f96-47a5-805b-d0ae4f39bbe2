<script lang="ts">
	import { enhance } from '$app/forms';
	import type { ActionData } from './$types';
	import { locale } from '$lib/stores/locale';
	import {
		Button,
		AuthLayout,
		FormInput,
		AlertMessage,
		PasswordStrengthMeter
	} from '$lib/components/ui';
	import { page } from '$app/stores';

	let { form, data }: { form: ActionData, data: any } = $props();
	let currentLocale = $state($locale);

	locale.subscribe(value => {
		currentLocale = value;
	});

	let password = $state('');
	let confirmPassword = $state('');
	let showPasswordRequirements = $state(false);
	let passwordsMatch = $state(true);

	function checkPasswordsMatch() {
		passwordsMatch = !confirmPassword || password === confirmPassword;
	}

	const token = $derived($page.url.searchParams.get('token') || '');
	const isValidToken = $derived(data?.isValidToken || false);
</script>

<AuthLayout
	title="Reset Password"
	backHref="/auth/login"
>
	{#if form?.success}
		<AlertMessage
			type="success"
			message={form.message}
			actionText="Return to login"
			actionHref="/auth/login"
		/>
	{:else if !isValidToken}
		<AlertMessage
			type="error"
			message="Invalid or expired password reset link."
			actionText="Request a new password reset link"
			actionHref="/auth/forgot-password"
		/>
	{:else}
		<form class="mt-10 space-y-8" method="POST" action="?/resetPassword" use:enhance>
			<input type="hidden" name="token" value={token} />

			<div>
				<FormInput
					id="password"
					name="password"
					type="password"
					label="New Password"
					value={password}
					onChange={(e: Event) => {
						password = (e.target as HTMLInputElement).value;
						showPasswordRequirements = true;
					}}
					onBlur={() => showPasswordRequirements = false}
					required
					placeholder="Enter your new password"
				/>

				<PasswordStrengthMeter
					{password}
					showRequirements={showPasswordRequirements}
				/>
			</div>

			<FormInput
				id="confirmPassword"
				name="confirmPassword"
				type="password"
				label="Confirm New Password"
				placeholder="Confirm your new password"
				value={confirmPassword}
				onChange={(e: Event) => {
					confirmPassword = (e.target as HTMLInputElement).value;
					checkPasswordsMatch();
				}}
				required
				helpText={confirmPassword && !passwordsMatch ? "Passwords do not match" : ""}
			/>

			{#if form?.error}
				<AlertMessage
					type="error"
					message={form.message}
				/>
			{/if}

			<div class="pt-2">
				<Button
					type="submit"
					variant="primary"
					size="lg"
					class="w-full"
					disabled={!passwordsMatch}
				>
					Reset Password
				</Button>
			</div>
		</form>
	{/if}
</AuthLayout>
